<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大模型的'未来之路'：生态与应用展望</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            padding: 40px 0;
        }
        .left-content {
            width: 80%;
            padding-left: 70px;
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .right-content {
            width: 50%;
            padding-left: 20px;
            padding-right: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 40px;
            color: #006064;
        }
        .summary-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
        }
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .summary-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .summary-icon {
            background-color: #00838f;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .summary-title {
            font-size: 22px;
            font-weight: 600;
            color: #006064;
        }
        .summary-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .highlight {
            color: #00838f;
            font-weight: 600;
        }
        .future-visual {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        .roadmap-image {
            width: 100%;
            height: 200px;
            background-color: #e0f7fa;
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 25px;
            overflow: hidden;
            position: relative;
        }
        .roadmap-icon {
            font-size: 80px;
            color: #00838f;
            z-index: 2;
        }
        .roadmap-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(0, 131, 143, 0.1), rgba(0, 96, 100, 0.05));
            z-index: 1;
        }
        .trend-list {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }
        .trend-item {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 12px;
            padding: 15px;
            transition: transform 0.3s ease;
        }
        .trend-item:hover {
            transform: translateX(10px);
            background-color: #e0f7fa;
        }
        .trend-icon {
            color: #00838f;
            margin-right: 15px;
            font-size: 24px;
            flex-shrink: 0;
        }
        .trend-text {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }
        .transition-hint {
            margin-top: 20px;
            padding: 15px;
            background: linear-gradient(90deg, rgba(0, 131, 143, 0.2) 0%, rgba(0, 131, 143, 0.1) 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        .transition-text {
            font-size: 18px;
            font-weight: 600;
            color: #00838f;
        }
        .transition-arrow {
            font-size: 24px;
            color: #00838f;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: translateX(0); }
            50% { transform: translateX(10px); }
            100% { transform: translateX(0); }
        }
        .footer {
            padding: 20px 70px;
            color: rgba(0, 96, 100, 0.7);
            font-size: 16px;
            text-align: right;
        }
        .glow {
            position: absolute;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(0, 131, 143, 0.3) 0%, rgba(0, 131, 143, 0) 70%);
            z-index: 0;
        }
        .glow-1 {
            top: -50px;
            right: -50px;
        }
        .glow-2 {
            bottom: -50px;
            left: -50px;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <div class="left-content">
                <h1 class="title">大模型的'未来之路'：生态与应用展望</h1>
                
                <div class="summary-card">
                    <div class="summary-header">
                        <div class="summary-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="summary-title">培训内容回顾</div>
                    </div>
                    <div class="summary-content">
                        • 大模型<span class="highlight">生态系统</span>：模型提供商、云平台、开发者、用户<br>
                        • <span class="highlight">应用场景</span>：内容创作、知识问答、代码生成等<br>
                        • <span class="highlight">行业案例</span>：金融、医疗、教育、制造、零售<br>
                        • <span class="highlight">开发流程</span>：需求分析、模型选择、提示工程等<br>
                        • <span class="highlight">挑战与解决方案</span>：幻觉、安全、资源、可解释性、偏见
                    </div>
                </div>
                

                <div class="summary-card">
                    <div class="summary-header">
                        <div class="summary-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="summary-title">未来展望</div>
                    </div>
                    <div class="summary-content">
                        • 我们已探索了大模型的<span class="highlight">生态系统</span>和<span class="highlight">应用场景</span><br>
                        • 接下来将思考AI技术如何改变我们的<span class="highlight">工作方式</span>和<span class="highlight">思维模式</span><br>
                        • 大模型技术不仅改变了我们与技术的交互方式<br>
                        • 更将深刻影响我们的<span class="highlight">工作思维</span>、<span class="highlight">技能需求</span>、<span class="highlight">职业发展</span>和<span class="highlight">伦理责任</span>
                    </div>
                </div>
            </div>
            
            <div class="right-content">
                <div class="future-visual">
                    <div class="glow glow-1"></div>
                    <div class="glow glow-2"></div>
                    
                    <div class="roadmap-image">
                        <div class="roadmap-bg"></div>
                        <i class="fas fa-rocket roadmap-icon"></i>
                    </div>
                    
                    <div class="trend-list">
                        <div class="trend-item">
                            <i class="fas fa-brain trend-icon"></i>
                            <div class="trend-text">理解大语言模型基础</div>
                        </div>
                        
                        <div class="trend-item">
                            <i class="fas fa-network-wired trend-icon"></i>
                            <div class="trend-text">探索大模型生态系统</div>
                        </div>
                        
                        <div class="trend-item">
                            <i class="fas fa-puzzle-piece trend-icon"></i>
                            <div class="trend-text">掌握实际应用场景</div>
                        </div>
                        
                        <div class="trend-item">
                            <i class="fas fa-sync-alt trend-icon"></i>
                            <div class="trend-text">转变工作思维模式</div>
                        </div>
                        
                        <div class="trend-item">
                            <i class="fas fa-magic trend-icon"></i>
                            <div class="trend-text">精通提示词工程技巧</div>
                        </div>
                    </div>
                    
                    <div class="transition-hint">
                        <div class="transition-text">下一章：AI时代的工作思维转变</div>
                        <i class="fas fa-arrow-right transition-arrow"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第18页
        </div>
    </div>
</body>
</html>