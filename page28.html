<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词工程案例分析：从普通到卓越</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 30px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 25px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .cases-container {
            display: flex;
            flex-direction: column;
            gap: 18px;
            flex-grow: 1;
        }
        .case-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 18px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: flex;
            gap: 18px;
        }
        .case-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .case-icon-container {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .case-icon {
            color: white;
            font-size: 28px;
        }
        .case-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .case-title {
            font-size: 22px;
            font-weight: 700;
            color: #764ba2;
        }
        .prompts-comparison {
            display: flex;
            gap: 18px;
        }
        .prompt-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .prompt-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #764ba2;
        }
        .prompt-box {
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 12px;
            font-size: 15px;
            color: #555;
            line-height: 1.4;
            flex-grow: 1;
            position: relative;
        }
        .prompt-box.normal {
            border-left: 4px solid #999;
        }
        .prompt-box.optimized {
            border-left: 4px solid #764ba2;
        }
        .improvement-points {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 5px;
        }
        .improvement-tag {
            background-color: #e6e0ff;
            border-radius: 20px;
            padding: 4px 10px;
            font-size: 13px;
            font-weight: 500;
            color: #764ba2;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .comparison-visual {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            gap: 25px;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 18px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        .visual-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }
        .visual-item:hover {
            transform: scale(1.05);
        }
        .visual-icon {
            font-size: 40px;
            margin-bottom: 10px;
        }
        .normal-icon {
            color: #999;
        }
        .optimized-icon {
            color: #764ba2;
        }
        .visual-text {
            font-size: 18px;
            font-weight: 600;
        }
        .normal-text {
            color: #666;
        }
        .optimized-text {
            color: #764ba2;
        }
        .arrow-icon {
            color: #764ba2;
            font-size: 32px;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">提示词工程案例分析：从普通到卓越</h1>
            
            <div class="cases-container">
                <div class="case-card">
                    <div class="case-icon-container">
                        <i class="fas fa-envelope case-icon"></i>
                    </div>
                    <div class="case-content">
                        <div class="case-title">案例一：邮件撰写</div>
                        <div class="prompts-comparison">
                            <div class="prompt-column">
                                <div class="prompt-label">
                                    <i class="fas fa-minus-circle"></i>
                                    普通提示词
                                </div>
                                <div class="prompt-box normal">
                                    请帮我写一封申请加薪的邮件
                                </div>
                            </div>
                            <div class="prompt-column">
                                <div class="prompt-label">
                                    <i class="fas fa-plus-circle"></i>
                                    优化提示词
                                </div>
                                <div class="prompt-box optimized">
                                    你是一位人力资源专家，请帮我写一封申请加薪的邮件。我在公司已工作3年，最近完成了XX项目，为公司带来了XX效益。邮件需要专业、礼貌但坚定，字数控制在300字以内。请先给出邮件草稿，再分析其优缺点。
                                </div>
                                <div class="improvement-points">
                                    <div class="improvement-tag">
                                        <i class="fas fa-user-tag"></i>
                                        角色设定
                                    </div>
                                    <div class="improvement-tag">
                                        <i class="fas fa-info-circle"></i>
                                        上下文信息
                                    </div>
                                    <div class="improvement-tag">
                                        <i class="fas fa-filter"></i>
                                        约束条件
                                    </div>
                                    <div class="improvement-tag">
                                        <i class="fas fa-tasks"></i>
                                        分步思考
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="case-card">
                    <div class="case-icon-container">
                        <i class="fas fa-chart-line case-icon"></i>
                    </div>
                    <div class="case-content">
                        <div class="case-title">案例二：数据分析</div>
                        <div class="prompts-comparison">
                            <div class="prompt-column">
                                <div class="prompt-label">
                                    <i class="fas fa-minus-circle"></i>
                                    普通提示词
                                </div>
                                <div class="prompt-box normal">
                                    请分析这份销售数据
                                </div>
                            </div>
                            <div class="prompt-column">
                                <div class="prompt-label">
                                    <i class="fas fa-plus-circle"></i>
                                    优化提示词
                                </div>
                                <div class="prompt-box optimized">
                                    你是一位资深数据分析师，请分析这份销售数据（附件）。重点关注：1）各产品线的销售趋势；2）区域销售表现差异；3）潜在增长机会。请用图表展示关键发现，并用通俗易懂的语言解释数据洞察。
                                </div>
                                <div class="improvement-points">
                                    <div class="improvement-tag">
                                        <i class="fas fa-user-tag"></i>
                                        角色设定
                                    </div>
                                    <div class="improvement-tag">
                                        <i class="fas fa-bullseye"></i>
                                        明确目标
                                    </div>
                                    <div class="improvement-tag">
                                        <i class="fas fa-list-ul"></i>
                                        结构化要求
                                    </div>
                                    <div class="improvement-tag">
                                        <i class="fas fa-file-export"></i>
                                        输出格式
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="case-card">
                    <div class="case-icon-container">
                        <i class="fas fa-lightbulb case-icon"></i>
                    </div>
                    <div class="case-content">
                        <div class="case-title">案例三：创意生成</div>
                        <div class="prompts-comparison">
                            <div class="prompt-column">
                                <div class="prompt-label">
                                    <i class="fas fa-minus-circle"></i>
                                    普通提示词
                                </div>
                                <div class="prompt-box normal">
                                    请为我们的新产品想个名字
                                </div>
                            </div>
                            <div class="prompt-column">
                                <div class="prompt-label">
                                    <i class="fas fa-plus-circle"></i>
                                    优化提示词
                                </div>
                                <div class="prompt-box optimized">
                                    你是一位品牌命名专家，请为我们的环保水杯产品想5个创意名字。产品特点：可降解材料、保温效果好、时尚设计。目标用户：25-35岁都市白领。名字需要简洁、易记、有环保元素，并能体现产品的高端定位。
                                </div>
                                <div class="improvement-points">
                                    <div class="improvement-tag">
                                        <i class="fas fa-user-tag"></i>
                                        角色设定
                                    </div>
                                    <div class="improvement-tag">
                                        <i class="fas fa-info-circle"></i>
                                        详细背景
                                    </div>
                                    <div class="improvement-tag">
                                        <i class="fas fa-users"></i>
                                        目标用户
                                    </div>
                                    <div class="improvement-tag">
                                        <i class="fas fa-sliders-h"></i>
                                        具体要求
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="comparison-visual">
                <div class="visual-item">
                    <i class="fas fa-sword normal-icon visual-icon"></i>
                    <div class="visual-text normal-text">普通武器</div>
                </div>
                
                <i class="fas fa-long-arrow-alt-right arrow-icon"></i>
                
                <div class="visual-item">
                    <i class="fas fa-magic optimized-icon visual-icon"></i>
                    <div class="visual-text optimized-text">提示词工程</div>
                </div>
                
                <i class="fas fa-long-arrow-alt-right arrow-icon"></i>
                
                <div class="visual-item">
                    <i class="fas fa-hat-wizard optimized-icon visual-icon"></i>
                    <div class="visual-text optimized-text">神器</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第28页
        </div>
    </div>
</body>
</html>