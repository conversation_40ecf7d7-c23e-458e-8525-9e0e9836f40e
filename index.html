<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI培训幻灯片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
            margin: 0;
            padding: 20px 0;
            min-height: 100vh;
        }
        #presentation-container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 20px;
            width: 100%;
        }
        #slide-container {
            position: relative;
            width: 100%;
            max-width: 1280px;
            height: auto;
            min-height: 720px;
            background: #000;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            margin-bottom: 100px;
        }
        .slide-frame {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            opacity: 0;
            transition: opacity 0.5s ease;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            overflow: hidden;
        }
        .slide-frame.active {
            opacity: 1;
        }
        .controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50px;
            backdrop-filter: blur(10px);
            z-index: 1000;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .control-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 10px;
            border-radius: 50%;
            transition: all 0.3s;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .control-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }
        .control-btn:active {
            transform: scale(0.95);
        }
        #progress {
            position: fixed;
            top: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #2575fc 0%, #6a11cb 100%);
            transition: width 0.3s;
            z-index: 1000;
        }
        #page-indicator {
            color: white;
            margin: 0 15px;
            font-size: 14px;
            display: flex;
            align-items: center;
            min-width: 60px;
            justify-content: center;
        }
        .material-icons {
            font-size: 24px;
        }
        /* 加载动画 */
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            display: none;
        }
        @keyframes spin {
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div id="progress"></div>
    <div id="presentation-container">
        <div id="slide-container">
            <div class="loading" id="loading"></div>
            <iframe id="slide-frame-1" class="slide-frame active" src="page1.html" scrolling="no"></iframe>
            <iframe id="slide-frame-2" class="slide-frame" src="" scrolling="no"></iframe>
            <iframe id="slide-frame-3" class="slide-frame" src="" scrolling="no"></iframe>
        </div>
    </div>
    <div class="controls">
        <button class="control-btn" id="prev-btn" title="上一页">
            <span class="material-icons">arrow_back</span>
        </button>
        <button class="control-btn" id="play-pause-btn" title="播放/暂停">
            <span class="material-icons">play_arrow</span>
        </button>
        <button class="control-btn" id="next-btn" title="下一页">
            <span class="material-icons">arrow_forward</span>
        </button>
        <div id="page-indicator">1 / 31</div>
        <button class="control-btn" id="fullscreen-btn" title="全屏">
            <span class="material-icons">fullscreen</span>
        </button>
    </div>

    <script>
        const totalSlides = 31;
        let currentSlide = 1;
        let isPlaying = false;
        let slideInterval;
        let isTransitioning = false;
        
        const frames = [
            document.getElementById('slide-frame-1'),
            document.getElementById('slide-frame-2'),
            document.getElementById('slide-frame-3')
        ];
        let activeFrameIndex = 0;
        
        const progressBar = document.getElementById('progress');
        const playPauseBtn = document.getElementById('play-pause-btn');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        const pageIndicator = document.getElementById('page-indicator');
        const loading = document.getElementById('loading');

        // 预加载管理
        const preloadCache = new Set();
        
        function preloadSlide(slideNumber) {
            if (slideNumber > totalSlides || slideNumber < 1 || preloadCache.has(slideNumber)) {
                return;
            }
            
            const img = new Image();
            img.src = `page${slideNumber}.html`;
            preloadCache.add(slideNumber);
        }

        // 预加载当前页面的前后各两页
        function preloadAdjacentSlides(currentSlide) {
            for (let i = -2; i <= 2; i++) {
                const slideToPreload = currentSlide + i;
                if (slideToPreload >= 1 && slideToPreload <= totalSlides) {
                    preloadSlide(slideToPreload);
                }
            }
        }

        async function updateSlide() {
            if (isTransitioning) return;
            isTransitioning = true;
            loading.style.display = 'block';

            // 更新进度条和页码指示器
            progressBar.style.width = `${(currentSlide / totalSlides) * 100}%`;
            pageIndicator.textContent = `${currentSlide} / ${totalSlides}`;

            // 确定下一个要使用的frame
            const nextFrameIndex = (activeFrameIndex + 1) % frames.length;
            const nextFrame = frames[nextFrameIndex];

            // 设置下一帧的内容
            nextFrame.src = `page${currentSlide}.html`;

            // 等待下一页加载完成
            await new Promise(resolve => {
                nextFrame.onload = () => {
                    loading.style.display = 'none';
                    
                    // 动态调整容器高度
                    const slideContainer = document.getElementById('slide-container');
                    slideContainer.style.height = nextFrame.contentWindow.document.body.scrollHeight + 'px';
                    
                    // 切换显示
                    frames[activeFrameIndex].classList.remove('active');
                    nextFrame.classList.add('active');
                    
                    // 更新活动frame索引
                    activeFrameIndex = nextFrameIndex;
                    
                    // 预加载相邻页面
                    preloadAdjacentSlides(currentSlide);
                    
                    setTimeout(() => {
                        isTransitioning = false;
                    }, 500);
                    
                    resolve();
                };
            });
        }

        // 优化后的切换函数
        async function nextSlide() {
            if (isTransitioning || currentSlide >= totalSlides) return;
            currentSlide++;
            await updateSlide();
            if (currentSlide === totalSlides && isPlaying) {
                togglePlayPause();
            }
        }

        async function prevSlide() {
            if (isTransitioning || currentSlide <= 1) return;
            currentSlide--;
            await updateSlide();
        }

        function togglePlayPause() {
            isPlaying = !isPlaying;
            playPauseBtn.innerHTML = `<span class="material-icons">${isPlaying ? 'pause' : 'play_arrow'}</span>`;
            
            if (isPlaying) {
                slideInterval = setInterval(() => {
                    if (currentSlide < totalSlides) {
                        nextSlide();
                    } else {
                        togglePlayPause();
                    }
                }, 5000);
            } else {
                clearInterval(slideInterval);
            }
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
                fullscreenBtn.innerHTML = '<span class="material-icons">fullscreen_exit</span>';
            } else {
                document.exitFullscreen();
                fullscreenBtn.innerHTML = '<span class="material-icons">fullscreen</span>';
            }
        }

        // 移除自适应缩放函数，不再需要
        function updateScale() {
            // 不再需要缩放逻辑
        }

        // 监听窗口大小变化 - 移除缩放调用
        window.addEventListener('resize', () => {
            // 可以添加其他响应式逻辑
        });

        // 初始化时不再调用缩放
        // updateScale(); // 移除这行

        // 事件监听
        playPauseBtn.addEventListener('click', togglePlayPause);
        prevBtn.addEventListener('click', () => {
            if (isPlaying) togglePlayPause();
            prevSlide();
        });
        nextBtn.addEventListener('click', () => {
            if (isPlaying) togglePlayPause();
            nextSlide();
        });
        fullscreenBtn.addEventListener('click', toggleFullscreen);

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (isTransitioning) return;
            
            switch(e.key) {
                case 'ArrowRight':
                case 'Space':
                    e.preventDefault();
                    if (isPlaying) togglePlayPause();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    if (isPlaying) togglePlayPause();
                    prevSlide();
                    break;
                case 'f':
                case 'F':
                    toggleFullscreen();
                    break;
                case 'p':
                case 'P':
                    togglePlayPause();
                    break;
            }
        });

        // 添加鼠标点击切换
        document.addEventListener('click', (e) => {
            if (isTransitioning) return;
            
            const slideContainer = document.getElementById('slide-container');
            const rect = slideContainer.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const containerWidth = rect.width;
            
            // 点击左半部分上一页，右半部分下一页
            if (clickX < containerWidth / 2) {
                if (isPlaying) togglePlayPause();
                prevSlide();
            } else {
                if (isPlaying) togglePlayPause();
                nextSlide();
            }
        });

        // 初始化
        updateSlide();
        preloadAdjacentSlides(1); // 预加载第一页相邻的页面
    </script>
</body>
</html>
