<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词工程实战：工作场景应用</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .scenarios-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            flex-grow: 1;
        }
        .scenario-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .scenario-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .scenario-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .scenario-icon-container {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .scenario-icon {
            color: white;
            font-size: 24px;
        }
        .scenario-title {
            font-size: 20px;
            font-weight: 700;
            color: #0066cc;
        }
        .scenario-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .scenario-desc {
            font-size: 16px;
            color: #555;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .tips-list {
            margin-top: auto;
        }
        .tip-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        .tip-icon {
            color: #0066cc;
            font-size: 16px;
            margin-right: 8px;
            margin-top: 3px;
            flex-shrink: 0;
        }
        .tip-text {
            font-size: 14px;
            color: #555;
            line-height: 1.3;
        }
        .battlefield-visual {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: space-around;
        }
        .battlefield-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }
        .battlefield-item:hover {
            transform: scale(1.1);
        }
        .battlefield-icon {
            font-size: 36px;
            color: #0066cc;
            margin-bottom: 8px;
        }
        .battlefield-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">提示词工程实战：工作场景应用</h1>
            
            <div class="scenarios-grid">
                <div class="scenario-card">
                    <div class="scenario-header">
                        <div class="scenario-icon-container">
                            <i class="fas fa-pen-fancy scenario-icon"></i>
                        </div>
                        <div class="scenario-title">内容创作场景</div>
                    </div>
                    <div class="scenario-content">
                        <div class="scenario-desc">
                            撰写营销文案、产品描述、社交媒体帖子等
                        </div>
                        <div class="tips-list">
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">设定专业角色</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">提供品牌信息</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">给出风格示例</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">指定字数限制</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="scenario-card">
                    <div class="scenario-header">
                        <div class="scenario-icon-container">
                            <i class="fas fa-chart-line scenario-icon"></i>
                        </div>
                        <div class="scenario-title">数据分析场景</div>
                    </div>
                    <div class="scenario-content">
                        <div class="scenario-desc">
                            解读数据报告、生成数据摘要、提取关键洞察
                        </div>
                        <div class="tips-list">
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">提供数据背景</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">明确分析目标</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">指定输出格式</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">要求术语解释</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="scenario-card">
                    <div class="scenario-header">
                        <div class="scenario-icon-container">
                            <i class="fas fa-headset scenario-icon"></i>
                        </div>
                        <div class="scenario-title">客户服务场景</div>
                    </div>
                    <div class="scenario-content">
                        <div class="scenario-desc">
                            生成常见问题回答、处理客户投诉、撰写服务邮件
                        </div>
                        <div class="tips-list">
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">设定客服角色</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">提供客户背景</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">强调服务态度</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">指定回复格式</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="scenario-card">
                    <div class="scenario-header">
                        <div class="scenario-icon-container">
                            <i class="fas fa-graduation-cap scenario-icon"></i>
                        </div>
                        <div class="scenario-title">学习培训场景</div>
                    </div>
                    <div class="scenario-content">
                        <div class="scenario-desc">
                            生成学习材料、设计培训课程、解答专业问题
                        </div>
                        <div class="tips-list">
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">设定教师角色</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">提供学员背景</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">明确学习目标</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">指定内容难度</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="scenario-card">
                    <div class="scenario-header">
                        <div class="scenario-icon-container">
                            <i class="fas fa-tasks scenario-icon"></i>
                        </div>
                        <div class="scenario-title">项目管理场景</div>
                    </div>
                    <div class="scenario-content">
                        <div class="scenario-desc">
                            生成项目计划、风险评估、进度报告
                        </div>
                        <div class="tips-list">
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">设定项目经理角色</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">提供项目背景</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">明确管理目标</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">指定报告格式</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="scenario-card">
                    <div class="scenario-header">
                        <div class="scenario-icon-container">
                            <i class="fas fa-code scenario-icon"></i>
                        </div>
                        <div class="scenario-title">代码开发场景</div>
                    </div>
                    <div class="scenario-content">
                        <div class="scenario-desc">
                            生成代码片段、解释代码逻辑、调试程序问题
                        </div>
                        <div class="tips-list">
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">设定开发者角色</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">提供技术背景</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">明确功能需求</div>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle tip-icon"></i>
                                <div class="tip-text">指定编程语言</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="battlefield-visual">
                <div class="battlefield-item">
                    <i class="fas fa-briefcase battlefield-icon"></i>
                    <div class="battlefield-text">不同战场</div>
                </div>
                
                <i class="fas fa-plus text-blue-600 text-2xl"></i>
                
                <div class="battlefield-item">
                    <i class="fas fa-magic battlefield-icon"></i>
                    <div class="battlefield-text">提示词工程</div>
                </div>
                
                <i class="fas fa-equals text-blue-600 text-2xl"></i>
                
                <div class="battlefield-item">
                    <i class="fas fa-trophy battlefield-icon"></i>
                    <div class="battlefield-text">制胜法宝</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第27页
        </div>
    </div>
</body>
</html>