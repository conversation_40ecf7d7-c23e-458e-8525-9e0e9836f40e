<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大语言模型是什么？</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            padding: 40px 0;
        }
        .left-content {
            width: 50%;
            padding-left: 70px;
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .right-content {
            width: 50%;
            padding-left: 20px;
            padding-right: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 40px;
            color: #003366;
        }
        .concept-card {
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .concept-card:hover {
            transform: translateY(-5px);
        }
        .concept-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .concept-icon {
            background-color: #0066cc;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .concept-title {
            font-size: 22px;
            font-weight: 600;
            color: #003366;
        }
        .concept-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .highlight {
            color: #0066cc;
            font-weight: 600;
        }
        .model-visual {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 500px;
            position: relative;
        }
        .model-brain {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 30px;
        }
        .brain-icon {
            font-size: 80px;
            color: #0066cc;
            margin-bottom: 15px;
        }
        .brain-text {
            font-size: 24px;
            font-weight: 600;
            color: #003366;
            text-align: center;
        }
        .model-components {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .component {
            width: 45%;
            background-color: #f0f7ff;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        .component-icon {
            font-size: 40px;
            color: #0066cc;
            margin-bottom: 10px;
        }
        .component-title {
            font-size: 20px;
            font-weight: 600;
            color: #003366;
            margin-bottom: 8px;
        }
        .component-desc {
            font-size: 16px;
            color: #555;
        }
        .model-workflow {
            text-align: center;
        }
        .workflow-title {
            font-size: 20px;
            font-weight: 600;
            color: #003366;
            margin-bottom: 15px;
        }
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .workflow-step {
            width: 30%;
            text-align: center;
        }
        .step-icon {
            font-size: 30px;
            color: #0066cc;
            margin-bottom: 8px;
        }
        .step-text {
            font-size: 16px;
            color: #333;
        }
        .arrow-icon {
            color: #0066cc;
            font-size: 30px;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(0, 51, 102, 0.7);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <div class="left-content">
                <h1 class="title">大语言模型是什么？</h1>
                
                <div class="concept-card">
                    <div class="concept-header">
                        <div class="concept-icon">
                            <i class="material-icons">psychology</i>
                        </div>
                        <div class="concept-title">通俗解释</div>
                    </div>
                    <div class="concept-content">
                        一个由<span class="highlight">巨量数据</span>训练而成的<span class="highlight">超级图书管理员</span>或<span class="highlight">知识渊博的魔法师</span>
                    </div>
                </div>
                
                <div class="concept-card">
                    <div class="concept-header">
                        <div class="concept-icon">
                            <i class="material-icons">biotech</i>
                        </div>
                        <div class="concept-title">本质构成</div>
                    </div>
                    <div class="concept-content">
                        两个核心组件：<span class="highlight">参数文件（DNA）</span>和<span class="highlight">代码文件（大脑）</span>
                    </div>
                </div>
                
                <div class="concept-card">
                    <div class="concept-header">
                        <div class="concept-icon">
                            <i class="material-icons">lightbulb</i>
                        </div>
                        <div class="concept-title">工作原理</div>
                    </div>
                    <div class="concept-content">
                        通过神经网络<span class="highlight">预测下一个单词</span>的概率，就像一个<span class="highlight">会说话的百科全书</span>
                    </div>
                </div>
            </div>
            
            <div class="right-content">
                <div class="model-visual">
                    <div class="model-brain">
                        <i class="material-icons brain-icon">smart_toy</i>
                        <div class="brain-text">大语言模型</div>
                    </div>
                    
                    <div class="model-components">
                        <div class="component">
                            <i class="material-icons component-icon">storage</i>
                            <div class="component-title">参数文件 (DNA)</div>
                            <div class="component-desc">包含数以亿计的权重，存储模型学到的知识</div>
                        </div>
                        
                        <div class="component">
                            <i class="material-icons component-icon">code</i>
                            <div class="component-title">代码文件 (大脑)</div>
                            <div class="component-desc">指导如何使用参数处理输入并产生输出</div>
                        </div>
                    </div>
                    
                    <div class="model-workflow">
                        <div class="workflow-title">工作流程</div>
                        <div class="workflow-steps">
                            <div class="workflow-step">
                                <i class="material-icons step-icon">input</i>
                                <div class="step-text">输入文本</div>
                            </div>
                            
                            <i class="material-icons arrow-icon">arrow_forward</i>
                            
                            <div class="workflow-step">
                                <i class="material-icons step-icon">analytics</i>
                                <div class="step-text">计算概率</div>
                            </div>
                            
                            <i class="material-icons arrow-icon">arrow_forward</i>
                            
                            <div class="workflow-step">
                                <i class="material-icons step-icon">output</i>
                                <div class="step-text">生成回复</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第3页
        </div>
    </div>
</body>
</html>