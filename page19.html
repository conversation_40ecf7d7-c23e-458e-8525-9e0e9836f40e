<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI时代的工作思维转变：从执行者到指挥家</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .main-container {
            display: flex;
            gap: 30px;
            flex-grow: 1;
        }
        .left-panel {
            width: 55%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .right-panel {
            width: 45%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .card-icon {
            background-color: #764ba2;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .card-title {
            font-size: 24px;
            font-weight: 700;
            color: #764ba2;
        }
        .comparison-container {
            display: flex;
            gap: 20px;
            margin-top: 10px;
            flex-grow: 1;
        }
        .comparison-column {
            flex: 1;
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }
        .comparison-title {
            font-size: 20px;
            font-weight: 700;
            color: #764ba2;
            margin-bottom: 15px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .comparison-title i {
            margin-right: 8px;
        }
        .comparison-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        .comparison-icon {
            color: #764ba2;
            margin-right: 10px;
            margin-top: 3px;
            flex-shrink: 0;
        }
        .comparison-text {
            font-size: 16px;
            color: #333;
            line-height: 1.4;
        }
        .highlight {
            color: #764ba2;
            font-weight: 700;
        }
        .shift-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 10px;
            flex-grow: 1;
        }
        .shift-item {
            display: flex;
            align-items: center;
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .shift-item:hover {
            transform: translateX(10px);
            background-color: #f0e6ff;
        }
        .shift-icon-container {
            width: 40px;
            height: 40px;
            background-color: #764ba2;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .shift-icon {
            color: white;
            font-size: 20px;
        }
        .shift-content {
            flex-grow: 1;
        }
        .shift-from {
            font-size: 16px;
            color: #666;
            text-decoration: line-through;
            margin-bottom: 5px;
        }
        .shift-to {
            font-size: 18px;
            font-weight: 600;
            color: #764ba2;
        }
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 10px;
            flex-grow: 1;
        }
        .skill-item {
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }
        .skill-item:hover {
            transform: translateY(-5px);
            background-color: #f0e6ff;
        }
        .skill-icon {
            width: 50px;
            height: 50px;
            background-color: #764ba2;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
        }
        .skill-icon i {
            color: white;
            font-size: 24px;
        }
        .skill-title {
            font-size: 18px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 5px;
        }
        .skill-desc {
            font-size: 14px;
            color: #666;
        }
        .visual-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
        }
        .visual-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 30px;
        }
        .visual-icon {
            font-size: 60px;
            color: #764ba2;
            margin-bottom: 15px;
        }
        .visual-title {
            font-size: 20px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 5px;
            text-align: center;
        }
        .visual-desc {
            font-size: 16px;
            color: #666;
            text-align: center;
            max-width: 150px;
        }
        .arrow-icon {
            color: #764ba2;
            font-size: 40px;
            margin: 0 15px;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">AI时代的工作思维转变：从执行者到指挥家</h1>
            
            <div class="main-container">
                <div class="left-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="card-title">工作思维对比</div>
                        </div>
                        
                        <div class="comparison-container">
                            <div class="comparison-column">
                                <div class="comparison-title">
                                    <i class="fas fa-history"></i>
                                    传统工作思维
                                </div>
                                
                                <div class="comparison-item">
                                    <i class="fas fa-check-circle comparison-icon"></i>
                                    <div class="comparison-text"><span class="highlight">执行为主</span>，强调个人技能和经验</div>
                                </div>
                                
                                <div class="comparison-item">
                                    <i class="fas fa-check-circle comparison-icon"></i>
                                    <div class="comparison-text">工作内容<span class="highlight">相对固定</span></div>
                                </div>
                                
                                <div class="comparison-item">
                                    <i class="fas fa-check-circle comparison-icon"></i>
                                    <div class="comparison-text">学习曲线<span class="highlight">平缓</span></div>
                                </div>
                                
                                <div class="comparison-item">
                                    <i class="fas fa-check-circle comparison-icon"></i>
                                    <div class="comparison-text">价值创造<span class="highlight">依赖个人能力</span></div>
                                </div>
                            </div>
                            
                            <div class="comparison-column">
                                <div class="comparison-title">
                                    <i class="fas fa-rocket"></i>
                                    AI时代工作思维
                                </div>
                                
                                <div class="comparison-item">
                                    <i class="fas fa-check-circle comparison-icon"></i>
                                    <div class="comparison-text"><span class="highlight">指挥和协作</span>为主，强调与AI工具协同</div>
                                </div>
                                
                                <div class="comparison-item">
                                    <i class="fas fa-check-circle comparison-icon"></i>
                                    <div class="comparison-text">工作内容<span class="highlight">创新多变</span></div>
                                </div>
                                
                                <div class="comparison-item">
                                    <i class="fas fa-check-circle comparison-icon"></i>
                                    <div class="comparison-text"><span class="highlight">持续学习</span>成为常态</div>
                                </div>
                                
                                <div class="comparison-item">
                                    <i class="fas fa-check-circle comparison-icon"></i>
                                    <div class="comparison-text">价值创造<span class="highlight">依赖人机协作</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-arrows-alt-h"></i>
                            </div>
                            <div class="card-title">思维转变核心</div>
                        </div>
                        
                        <div class="shift-container">
                            <div class="shift-item">
                                <div class="shift-icon-container">
                                    <i class="fas fa-tasks shift-icon"></i>
                                </div>
                                <div class="shift-content">
                                    <div class="shift-from">如何做</div>
                                    <div class="shift-to">做什么</div>
                                </div>
                            </div>
                            
                            <div class="shift-item">
                                <div class="shift-icon-container">
                                    <i class="fas fa-tools shift-icon"></i>
                                </div>
                                <div class="shift-content">
                                    <div class="shift-from">掌握技能</div>
                                    <div class="shift-to">掌握工具</div>
                                </div>
                            </div>
                            
                            <div class="shift-item">
                                <div class="shift-icon-container">
                                    <i class="fas fa-user shift-icon"></i>
                                </div>
                                <div class="shift-content">
                                    <div class="shift-from">独立完成</div>
                                    <div class="shift-to">人机协作</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="right-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="card-title">AI时代的工作能力</div>
                        </div>
                        
                        <div class="skills-grid">
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fas fa-question-circle"></i>
                                </div>
                                <div class="skill-title">问题定义能力</div>
                                <div class="skill-desc">精准识别和定义需要解决的问题</div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="skill-title">工具选择能力</div>
                                <div class="skill-desc">选择合适的AI工具解决问题</div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="skill-title">结果评估能力</div>
                                <div class="skill-desc">评估AI输出结果的质量和有效性</div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-icon">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <div class="skill-title">持续学习能力</div>
                                <div class="skill-desc">不断学习新工具和新方法</div>
                            </div>
                        </div>
                        
                        <div class="visual-container">
                            <div class="visual-item">
                                <i class="fas fa-hammer visual-icon"></i>
                                <div class="visual-title">勤劳的工匠</div>
                                <div class="visual-desc">传统工作者</div>
                            </div>
                            
                            <i class="fas fa-long-arrow-alt-right arrow-icon"></i>
                            
                            <div class="visual-item">
                                <i class="fas fa-music visual-icon"></i>
                                <div class="visual-title">指挥家</div>
                                <div class="visual-desc">AI时代工作者</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第19页
        </div>
    </div>
</body>
</html>