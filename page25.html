<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词工程基础：与AI有效沟通的艺术</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .main-container {
            display: flex;
            gap: 25px;
            flex-grow: 1;
        }
        .left-panel {
            width: 55%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .right-panel {
            width: 45%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .card-icon {
            background-color: #764ba2;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .card-title {
            font-size: 24px;
            font-weight: 700;
            color: #764ba2;
        }
        .definition-content {
            display: flex;
            align-items: center;
            margin-top: 10px;
            gap: 20px;
        }
        .definition-icon {
            font-size: 60px;
            color: #764ba2;
            flex-shrink: 0;
        }
        .definition-text {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .importance-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 10px;
        }
        .importance-item {
            display: flex;
            align-items: center;
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .importance-item:hover {
            transform: translateX(10px);
            background-color: #f0e6ff;
        }
        .importance-icon {
            color: #764ba2;
            font-size: 24px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .importance-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .principles-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 10px;
        }
        .principle-item {
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .principle-item:hover {
            transform: translateY(-5px);
            background-color: #f0e6ff;
        }
        .principle-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .principle-icon {
            color: #764ba2;
            font-size: 24px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        .principle-title {
            font-size: 18px;
            font-weight: 700;
            color: #764ba2;
        }
        .principle-desc {
            font-size: 16px;
            color: #555;
            line-height: 1.4;
        }
        .structure-container {
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .structure-item {
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        .structure-item:hover {
            transform: translateX(10px);
            background-color: #f0e6ff;
        }
        .structure-icon-container {
            width: 40px;
            height: 40px;
            background-color: #764ba2;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .structure-icon {
            color: white;
            font-size: 20px;
        }
        .structure-content {
            flex-grow: 1;
        }
        .structure-title {
            font-size: 18px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 5px;
        }
        .structure-desc {
            font-size: 16px;
            color: #555;
        }
        .magic-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
        }
        .magic-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            margin: 0 20px;
            transition: all 0.3s ease;
        }
        .magic-item:hover {
            transform: scale(1.1);
        }
        .magic-icon {
            font-size: 40px;
            color: #764ba2;
            margin-bottom: 10px;
        }
        .magic-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">提示词工程基础：与AI有效沟通的艺术</h1>
            
            <div class="main-container">
                <div class="left-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-comment-dots"></i>
                            </div>
                            <div class="card-title">什么是提示词（Prompt）</div>
                        </div>
                        
                        <div class="definition-content">
                            <i class="fas fa-keyboard definition-icon"></i>
                            <div class="definition-text">
                                给大语言模型的<span class="text-purple-700 font-bold">输入文本</span>，用于引导模型产生<span class="text-purple-700 font-bold">期望的输出</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="card-title">提示词的重要性</div>
                        </div>
                        
                        <div class="importance-list">
                            <div class="importance-item">
                                <i class="fas fa-check-circle importance-icon"></i>
                                <div class="importance-text">显著提高模型输出质量</div>
                            </div>
                            
                            <div class="importance-item">
                                <i class="fas fa-check-circle importance-icon"></i>
                                <div class="importance-text">减少错误和幻觉</div>
                            </div>
                            
                            <div class="importance-item">
                                <i class="fas fa-check-circle importance-icon"></i>
                                <div class="importance-text">提高工作效率和准确性</div>
                            </div>
                            
                            <div class="importance-item">
                                <i class="fas fa-check-circle importance-icon"></i>
                                <div class="importance-text">实现更复杂和专业的任务</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="right-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="card-title">提示词的基本原则</div>
                        </div>
                        
                        <div class="principles-grid">
                            <div class="principle-item">
                                <div class="principle-header">
                                    <i class="fas fa-bullseye principle-icon"></i>
                                    <div class="principle-title">清晰明确</div>
                                </div>
                                <div class="principle-desc">使用简洁、具体的语言</div>
                            </div>
                            
                            <div class="principle-item">
                                <div class="principle-header">
                                    <i class="fas fa-info-circle principle-icon"></i>
                                    <div class="principle-title">提供上下文</div>
                                </div>
                                <div class="principle-desc">提供足够的背景信息</div>
                            </div>
                            
                            <div class="principle-item">
                                <div class="principle-header">
                                    <i class="fas fa-user-tag principle-icon"></i>
                                    <div class="principle-title">设定角色</div>
                                </div>
                                <div class="principle-desc">告诉模型它应该扮演什么角色</div>
                            </div>
                            
                            <div class="principle-item">
                                <div class="principle-header">
                                    <i class="fas fa-file-alt principle-icon"></i>
                                    <div class="principle-title">给出示例</div>
                                </div>
                                <div class="principle-desc">通过示例说明期望的输出格式</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <div class="card-title">提示词的结构</div>
                        </div>
                        
                        <div class="structure-container">
                            <div class="structure-item">
                                <div class="structure-icon-container">
                                    <i class="fas fa-tasks structure-icon"></i>
                                </div>
                                <div class="structure-content">
                                    <div class="structure-title">指令</div>
                                    <div class="structure-desc">告诉模型要做什么</div>
                                </div>
                            </div>
                            
                            <div class="structure-item">
                                <div class="structure-icon-container">
                                    <i class="fas fa-book-open structure-icon"></i>
                                </div>
                                <div class="structure-content">
                                    <div class="structure-title">上下文</div>
                                    <div class="structure-desc">提供背景信息</div>
                                </div>
                            </div>
                            
                            <div class="structure-item">
                                <div class="structure-icon-container">
                                    <i class="fas fa-database structure-icon"></i>
                                </div>
                                <div class="structure-content">
                                    <div class="structure-title">输入数据</div>
                                    <div class="structure-desc">需要处理的具体内容</div>
                                </div>
                            </div>
                            
                            <div class="structure-item">
                                <div class="structure-icon-container">
                                    <i class="fas fa-file-export structure-icon"></i>
                                </div>
                                <div class="structure-content">
                                    <div class="structure-title">输出格式</div>
                                    <div class="structure-desc">期望的输出格式</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="magic-visual">
                            <div class="magic-item">
                                <i class="fas fa-magic magic-icon"></i>
                                <div class="magic-text">魔法咒语</div>
                            </div>
                            
                            <i class="fas fa-plus text-white text-2xl"></i>
                            
                            <div class="magic-item">
                                <i class="fas fa-robot magic-icon"></i>
                                <div class="magic-text">AI精灵</div>
                            </div>
                            
                            <i class="fas fa-equals text-white text-2xl"></i>
                            
                            <div class="magic-item">
                                <i class="fas fa-star magic-icon"></i>
                                <div class="magic-text">理想结果</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第25页
        </div>
    </div>
</body>
</html>