<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI赋能未来：全篇总结与致谢</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .main-container {
            display: flex;
            gap: 25px;
            flex-grow: 1;
        }
        .left-panel {
            width: 60%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .right-panel {
            width: 40%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .card-icon {
            background-color: #764ba2;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .card-title {
            font-size: 24px;
            font-weight: 700;
            color: #764ba2;
        }
        .content-review {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 10px;
        }
        .section-item {
            display: flex;
            align-items: flex-start;
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .section-item:hover {
            transform: translateX(10px);
            background-color: #f0e6ff;
        }
        .section-number {
            width: 30px;
            height: 30px;
            background-color: #764ba2;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: 700;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .section-content {
            flex-grow: 1;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 5px;
        }
        .section-desc {
            font-size: 16px;
            color: #666;
        }
        .key-points {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 10px;
        }
        .key-point {
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        .key-point:hover {
            transform: translateY(-5px);
            background-color: #f0e6ff;
        }
        .key-icon {
            color: #764ba2;
            font-size: 24px;
            margin-right: 12px;
            flex-shrink: 0;
        }
        .key-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .learning-outcomes {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 10px;
        }
        .outcome-item {
            display: flex;
            align-items: center;
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .outcome-item:hover {
            transform: translateX(10px);
            background-color: #f0e6ff;
        }
        .outcome-icon {
            color: #764ba2;
            font-size: 24px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .outcome-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .thanks-content {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 10px;
        }
        .thanks-item {
            display: flex;
            align-items: center;
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .thanks-item:hover {
            transform: translateX(10px);
            background-color: #f0e6ff;
        }
        .thanks-icon {
            color: #764ba2;
            font-size: 24px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .thanks-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .resources {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .resource-tag {
            background-color: #e6e0ff;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 16px;
            font-weight: 500;
            color: #764ba2;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        .resource-tag:hover {
            transform: scale(1.05);
            background-color: #d1c7ff;
        }
        .conclusion {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .conclusion-text {
            font-size: 24px;
            font-weight: 700;
            color: #764ba2;
            text-align: center;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">AI赋能未来：全篇总结与致谢</h1>
            
            <div class="main-container">
                <div class="left-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="card-title">全篇内容回顾</div>
                        </div>
                        
                        <div class="content-review">
                            <div class="section-item">
                                <div class="section-number">1</div>
                                <div class="section-content">
                                    <div class="section-title">大语言模型基础知识</div>
                                    <div class="section-desc">概念、原理、核心术语、模型类型、训练过程</div>
                                </div>
                            </div>
                            
                            <div class="section-item">
                                <div class="section-number">2</div>
                                <div class="section-content">
                                    <div class="section-title">大模型生态与应用</div>
                                    <div class="section-desc">生态系统、应用场景、行业案例、开发流程</div>
                                </div>
                            </div>
                            
                            <div class="section-item">
                                <div class="section-number">3</div>
                                <div class="section-content">
                                    <div class="section-title">AI时代的工作思维转变</div>
                                    <div class="section-desc">工作技能、方法、职业发展、伦理责任</div>
                                </div>
                            </div>
                            
                            <div class="section-item">
                                <div class="section-number">4</div>
                                <div class="section-content">
                                    <div class="section-title">提示词工程实战</div>
                                    <div class="section-desc">基础理论、技巧、应用场景、案例分析</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="card-title">核心要点总结</div>
                        </div>
                        
                        <div class="key-points">
                            <div class="key-point">
                                <i class="fas fa-brain key-icon"></i>
                                <div class="key-text">理解大模型原理</div>
                            </div>
                            
                            <div class="key-point">
                                <i class="fas fa-network-wired key-icon"></i>
                                <div class="key-text">了解生态系统</div>
                            </div>
                            
                            <div class="key-point">
                                <i class="fas fa-sync-alt key-icon"></i>
                                <div class="key-text">掌握思维转变</div>
                            </div>
                            
                            <div class="key-point">
                                <i class="fas fa-magic key-icon"></i>
                                <div class="key-text">学会提示工程</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="right-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="card-title">学习收获</div>
                        </div>
                        
                        <div class="learning-outcomes">
                            <div class="outcome-item">
                                <i class="fas fa-cube outcome-icon"></i>
                                <div class="outcome-text">建立AI知识体系</div>
                            </div>
                            
                            <div class="outcome-item">
                                <i class="fas fa-handshake outcome-icon"></i>
                                <div class="outcome-text">掌握与AI协作方法</div>
                            </div>
                            
                            <div class="outcome-item">
                                <i class="fas fa-rocket outcome-icon"></i>
                                <div class="outcome-text">提升AI应用能力</div>
                            </div>
                            
                            <div class="outcome-item">
                                <i class="fas fa-lightbulb outcome-icon"></i>
                                <div class="outcome-text">培养创新思维</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="card-title">致谢</div>
                        </div>
                        
                        <div class="thanks-content">
                            <div class="thanks-item">
                                <i class="fas fa-users thanks-icon"></i>
                                <div class="thanks-text">感谢各位的参与和关注</div>
                            </div>
                            
                            <div class="thanks-item">
                                <i class="fas fa-robot thanks-icon"></i>
                                <div class="thanks-text">感谢AI技术带来的无限可能</div>
                            </div>
                            
                            <div class="thanks-item">
                                <i class="fas fa-users-cog thanks-icon"></i>
                                <div class="thanks-text">感谢团队的支持和贡献</div>
                            </div>
                        </div>
                        
                        <div class="resources">
                            <div class="resource-tag">
                                <i class="fas fa-link"></i>
                                <span>学习资源链接</span>
                            </div>
                            
                            <div class="resource-tag">
                                <i class="fas fa-tools"></i>
                                <span>AI工具推荐</span>
                            </div>
                            
                            <div class="resource-tag">
                                <i class="fas fa-comments"></i>
                                <span>交流群组</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="conclusion">
                <div class="conclusion-text">
                    <i class="fas fa-rocket mr-3"></i>
                    感谢聆听，让我们共同拥抱AI时代！
                    <i class="fas fa-rocket ml-3"></i>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第32页
        </div>
    </div>
</body>
</html>