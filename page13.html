<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大模型的'朋友圈'：生态系统概览</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&amp;display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 30px 50px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 25px;
            color: #003366;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .ecosystem-container {
            display: flex;
            gap: 25px;
            margin-bottom: 20px;
            flex-grow: 1;
        }
        .ecosystem-left {
            width: 55%;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .ecosystem-right {
            width: 45%;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .eco-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .eco-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
        }
        .eco-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .eco-icon {
            background-color: #0066cc;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .eco-title {
            font-size: 24px;
            font-weight: 700;
            color: #003366;
        }
        .highlight {
            color: #0066cc;
            font-weight: 700;
        }
        .model-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-top: 10px;
        }
        .model-item {
            background-color: #e6f7ff;
            border-radius: 10px;
            padding: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .model-item:hover {
            transform: scale(1.05);
            background-color: #bae7ff;
        }
        .model-icon {
            font-size: 28px;
            color: #0066cc;
            margin-bottom: 8px;
        }
        .model-name {
            font-size: 16px;
            font-weight: 600;
            color: #003366;
            text-align: center;
        }
        .model-company {
            font-size: 14px;
            color: #666;
            margin-top: 3px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .comparison-table th, .comparison-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        .comparison-table th {
            font-size: 16px;
            font-weight: 600;
            color: #0066cc;
            background-color: #f0f7ff;
        }
        .comparison-table td {
            font-size: 14px;
            color: #333;
        }
        .community-visual {
            position: relative;
            height: 220px;
            margin-top: 10px;
        }
        .center-circle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            background-color: #0066cc;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 16px;
            font-weight: 700;
            text-align: center;
            z-index: 2;
            box-shadow: 0 4px 15px rgba(0, 102, 204, 0.4);
        }
        .member-circle {
            position: absolute;
            width: 90px;
            height: 90px;
            background-color: #ffffff;
            border: 3px solid #0066cc;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            z-index: 1;
        }
        .member-circle:hover {
            transform: scale(1.1);
            z-index: 3;
        }
        .member-icon {
            font-size: 24px;
            color: #0066cc;
            margin-bottom: 5px;
        }
        .member-name {
            font-size: 13px;
            font-weight: 600;
            color: #003366;
            text-align: center;
        }
        .member-1 { top: 0; left: 50%; transform: translateX(-50%); }
        .member-2 { top: 50%; right: 0; transform: translateY(-50%); }
        .member-3 { bottom: 0; left: 50%; transform: translateX(-50%); }
        .member-4 { top: 50%; left: 0; transform: translateY(-50%); }
        .connector {
            position: absolute;
            background-color: rgba(0, 102, 204, 0.3);
            z-index: 0;
        }
        .connector-1 {
            width: 3px;
            height: 80px;
            top: 90px;
            left: 50%;
            transform: translateX(-50%);
        }
        .connector-2 {
            width: 80px;
            height: 3px;
            top: 50%;
            right: 90px;
            transform: translateY(-50%);
        }
        .connector-3 {
            width: 3px;
            height: 80px;
            bottom: 90px;
            left: 50%;
            transform: translateX(-50%);
        }
        .connector-4 {
            width: 80px;
            height: 3px;
            top: 50%;
            left: 90px;
            transform: translateY(-50%);
        }
        .trend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        .trend-icon {
            color: #0066cc;
            margin-right: 10px;
            font-size: 16px;
        }
        .trend-text {
            font-size: 15px;
            color: #333;
        }
        .footer {
            padding: 15px 50px;
            color: rgba(0, 51, 102, 0.7);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">大模型的'朋友圈'：生态系统概览</h1>
            
            <div class="ecosystem-container">
                <div class="ecosystem-left">
                    <div class="eco-card">
                        <div class="eco-header">
                            <div class="eco-icon">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div class="eco-title">生态系统组成</div>
                        </div>
                        <div class="community-visual">
                            <div class="center-circle">大模型<br>生态系统</div>
                            
                            <!-- Connectors -->
                            <div class="connector connector-1"></div>
                            <div class="connector connector-2"></div>
                            <div class="connector connector-3"></div>
                            <div class="connector connector-4"></div>
                            
                            <!-- Members -->
                            <div class="member-circle member-1">
                                <i class="fas fa-building member-icon"></i>
                                <div class="member-name">模型提供商</div>
                            </div>
                            <div class="member-circle member-2">
                                <i class="fas fa-cloud member-icon"></i>
                                <div class="member-name">云服务平台</div>
                            </div>
                            <div class="member-circle member-3">
                                <i class="fas fa-code member-icon"></i>
                                <div class="member-name">应用开发者</div>
                            </div>
                            <div class="member-circle member-4">
                                <i class="fas fa-users member-icon"></i>
                                <div class="member-name">终端用户</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="eco-card">
                        <div class="eco-header">
                            <div class="eco-icon">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <div class="eco-title">大模型家族树</div>
                        </div>
                        <div class="model-grid">
                            <div class="model-item">
                                <i class="fas fa-robot model-icon"></i>
                                <div class="model-name">GPT系列</div>
                                <div class="model-company">OpenAI</div>
                            </div>
                            <div class="model-item">
                                <i class="fas fa-horse model-icon"></i>
                                <div class="model-name">LLaMA系列</div>
                                <div class="model-company">Meta</div>
                            </div>
                            <div class="model-item">
                                <i class="fas fa-brain model-icon"></i>
                                <div class="model-name">Claude</div>
                                <div class="model-company">Anthropic</div>
                            </div>
                            <div class="model-item">
                                <i class="fas fa-gem model-icon"></i>
                                <div class="model-name">Gemini</div>
                                <div class="model-company">Google</div>
                            </div>
                            <div class="model-item">
                                <i class="fas fa-dragon model-icon"></i>
                                <div class="model-name">文心一言</div>
                                <div class="model-company">百度</div>
                            </div>
                            <div class="model-item">
                                <i class="fas fa-star model-icon"></i>
                                <div class="model-name">通义千问</div>
                                <div class="model-company">阿里</div>
                            </div>
                            <div class="model-item">
                                <i class="fas fa-cube model-icon"></i>
                                <div class="model-name">豆包</div>
                                <div class="model-company">字节</div>
                            </div>
                            <div class="model-item">
                                <i class="fas fa-water model-icon"></i>
                                <div class="model-name">DeepSeek</div>
                                <div class="model-company">幻方量化</div>
                            </div>
                            <div class="model-item">
                                <i class="fas fa-rocket model-icon"></i>
                                <div class="model-name">更多模型</div>
                                <div class="model-company">持续增长</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="ecosystem-right">
                    <div class="eco-card">
                        <div class="eco-header">
                            <div class="eco-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <div class="eco-title">开源 vs 闭源模型</div>
                        </div>
                        <table class="comparison-table">
                            <thead>
                                <tr>
                                    <th>特性</th>
                                    <th>开源模型</th>
                                    <th>闭源模型</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>代表</strong></td>
                                    <td>LLaMA、DeepSeek</td>
                                    <td>GPT-4、Claude</td>
                                </tr>
                                <tr>
                                    <td><strong>可访问性</strong></td>
                                    <td>高，可本地部署</td>
                                    <td>低，需API调用</td>
                                </tr>
                                <tr>
                                    <td><strong>定制性</strong></td>
                                    <td>高，可自由修改</td>
                                    <td>低，有限配置</td>
                                </tr>
                                <tr>
                                    <td><strong>性能</strong></td>
                                    <td>中到高</td>
                                    <td>通常顶尖</td>
                                </tr>
                                <tr>
                                    <td><strong>成本</strong></td>
                                    <td>前期高，长期低</td>
                                    <td>按使用付费</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="eco-card">
                        <div class="eco-header">
                            <div class="eco-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="eco-title">生态发展趋势</div>
                        </div>
                        <div class="trend-item">
                            <i class="fas fa-layer-group trend-icon"></i>
                            <div class="trend-text"><span class="highlight">多模态融合</span>：文本、图像、音频、视频统一处理</div>
                        </div>
                        <div class="trend-item">
                            <i class="fas fa-microscope trend-icon"></i>
                            <div class="trend-text"><span class="highlight">垂直领域专精</span>：针对特定行业的专业模型</div>
                        </div>
                        <div class="trend-item">
                            <i class="fas fa-compress-alt trend-icon"></i>
                            <div class="trend-text"><span class="highlight">小型化部署</span>：模型压缩与边缘计算</div>
                        </div>
                        <div class="trend-item">
                            <i class="fas fa-code-branch trend-icon"></i>
                            <div class="trend-text"><span class="highlight">开源生态繁荣</span>：社区贡献与协作创新</div>
                        </div>
                        <div class="trend-item">
                            <i class="fas fa-shield-alt trend-icon"></i>
                            <div class="trend-text"><span class="highlight">安全与伦理</span>：负责任的AI发展</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第13页
        </div>
    </div>

</body></html>