<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI时代的'新技能树'：必备工作技能</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            flex-grow: 1;
        }
        .skill-card {
            width: calc(33.33% - 14px);
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        .skill-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .skill-icon-container {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .skill-icon {
            color: white;
            font-size: 36px;
        }
        .skill-title {
            font-size: 22px;
            font-weight: 700;
            color: #333;
            margin-bottom: 12px;
        }
        .skill-desc {
            font-size: 16px;
            color: #666;
            line-height: 1.4;
            flex-grow: 1;
        }
        .skill-keywords {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 8px;
            margin-top: 15px;
        }
        .skill-keyword {
            background-color: #fff9e6;
            border-radius: 20px;
            padding: 5px 12px;
            font-size: 14px;
            font-weight: 600;
            color: #fa709a;
        }
        .hero-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 30px;
            gap: 40px;
        }
        .hero-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.3s ease;
        }
        .hero-item:hover {
            transform: scale(1.05);
        }
        .hero-icon {
            font-size: 40px;
            color: #fa709a;
            margin-bottom: 10px;
        }
        .hero-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            text-align: center;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">AI时代的'新技能树'：必备工作技能</h1>
            
            <div class="skills-container">
                <div class="skill-card">
                    <div class="skill-icon-container">
                        <i class="fas fa-comment-dots skill-icon"></i>
                    </div>
                    <div class="skill-title">提示工程</div>
                    <div class="skill-desc">学会如何与AI有效沟通，设计精准的提示词，引导AI产生期望的输出</div>
                    <div class="skill-keywords">
                        <div class="skill-keyword">精准提问</div>
                        <div class="skill-keyword">上下文构建</div>
                        <div class="skill-keyword">迭代优化</div>
                    </div>
                </div>
                
                <div class="skill-card">
                    <div class="skill-icon-container">
                        <i class="fas fa-tools skill-icon"></i>
                    </div>
                    <div class="skill-title">AI工具选择与评估</div>
                    <div class="skill-desc">了解各种AI工具的特点和适用场景，能够根据需求选择最合适的工具</div>
                    <div class="skill-keywords">
                        <div class="skill-keyword">工具对比</div>
                        <div class="skill-keyword">场景匹配</div>
                        <div class="skill-keyword">效果评估</div>
                    </div>
                </div>
                
                <div class="skill-card">
                    <div class="skill-icon-container">
                        <i class="fas fa-database skill-icon"></i>
                    </div>
                    <div class="skill-title">数据素养</div>
                    <div class="skill-desc">理解数据的价值，能够收集、清洗、分析和解读数据，为AI提供高质量的输入</div>
                    <div class="skill-keywords">
                        <div class="skill-keyword">数据清洗</div>
                        <div class="skill-keyword">质量评估</div>
                        <div class="skill-keyword">数据解读</div>
                    </div>
                </div>
                
                <div class="skill-card">
                    <div class="skill-icon-container">
                        <i class="fas fa-brain skill-icon"></i>
                    </div>
                    <div class="skill-title">批判性思维</div>
                    <div class="skill-desc">能够质疑和评估AI的输出，识别潜在的错误和偏见，不盲目接受AI结果</div>
                    <div class="skill-keywords">
                        <div class="skill-keyword">逻辑分析</div>
                        <div class="skill-keyword">事实核查</div>
                        <div class="skill-keyword">偏见识别</div>
                    </div>
                </div>
                
                <div class="skill-card">
                    <div class="skill-icon-container">
                        <i class="fas fa-project-diagram skill-icon"></i>
                    </div>
                    <div class="skill-title">跨学科知识</div>
                    <div class="skill-desc">结合不同领域的知识，创造性地应用AI解决复杂问题，打破学科壁垒</div>
                    <div class="skill-keywords">
                        <div class="skill-keyword">知识融合</div>
                        <div class="skill-keyword">创新思维</div>
                        <div class="skill-keyword">跨界应用</div>
                    </div>
                </div>
                
                <div class="skill-card">
                    <div class="skill-icon-container">
                        <i class="fas fa-graduation-cap skill-icon"></i>
                    </div>
                    <div class="skill-title">持续学习能力</div>
                    <div class="skill-desc">保持好奇心和学习热情，及时跟进AI技术的最新发展，不断更新知识体系</div>
                    <div class="skill-keywords">
                        <div class="skill-keyword">自我更新</div>
                        <div class="skill-keyword">技术追踪</div>
                        <div class="skill-keyword">实践应用</div>
                    </div>
                </div>
            </div>
            
            <div class="hero-container">
                <div class="hero-item">
                    <i class="fas fa-user-ninja hero-icon"></i>
                    <div class="hero-text">普通员工</div>
                </div>
                
                <i class="fas fa-plus hero-icon"></i>
                
                <div class="hero-item">
                    <i class="fas fa-robot hero-icon"></i>
                    <div class="hero-text">AI技能</div>
                </div>
                
                <i class="fas fa-equals hero-icon"></i>
                
                <div class="hero-item">
                    <i class="fas fa-user-astronaut hero-icon"></i>
                    <div class="hero-text">AI时代超级英雄</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第20页
        </div>
    </div>
</body>
</html>