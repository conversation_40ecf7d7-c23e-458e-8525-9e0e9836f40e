<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大模型的'实战'：行业应用案例</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 70px;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
        }
        .industry-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: auto auto;
            gap: 20px;
            flex-grow: 1;
        }
        .industry-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .industry-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .industry-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .industry-icon {
            background-color: #f5576c;
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 12px;
            flex-shrink: 0;
            font-size: 22px;
        }
        .industry-title {
            font-size: 22px;
            font-weight: 600;
            color: #333;
        }
        .industry-applications {
            font-size: 18px;
            color: #333;
            line-height: 1.4;
            margin-bottom: 12px;
            flex-grow: 1;
        }
        .application-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        .application-icon {
            color: #f5576c;
            margin-right: 8px;
            font-size: 16px;
        }
        .case-study {
            background-color: #fff0f3;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            color: #333;
            line-height: 1.4;
        }
        .case-title {
            font-weight: 600;
            color: #f5576c;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
        }
        .case-icon {
            margin-right: 8px;
            font-size: 18px;
        }
        .case-content {
            display: flex;
            align-items: center;
        }
        .case-text {
            flex-grow: 1;
        }
        .case-metric {
            background-color: #f5576c;
            color: white;
            border-radius: 20px;
            padding: 4px 10px;
            font-weight: 600;
            font-size: 16px;
            margin-left: 10px;
            white-space: nowrap;
        }
        .highlight {
            color: #f5576c;
            font-weight: 600;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">大模型的'实战'：行业应用案例</h1>
            
            <div class="industry-grid">
                <div class="industry-card">
                    <div class="industry-header">
                        <div class="industry-icon">
                            <i class="material-icons">account_balance</i>
                        </div>
                        <div class="industry-title">金融行业</div>
                    </div>
                    <div class="industry-applications">
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            风险评估
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            投资分析
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            智能客服
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            反欺诈
                        </div>
                    </div>
                    <div class="case-study">
                        <div class="case-title">
                            <i class="material-icons case-icon">stars</i>
                            案例
                        </div>
                        <div class="case-content">
                            <div class="case-text">某银行使用大模型分析客户信用风险</div>
                            <div class="case-metric">准确率↑30%</div>
                        </div>
                    </div>
                </div>
                
                <div class="industry-card">
                    <div class="industry-header">
                        <div class="industry-icon">
                            <i class="material-icons">health_and_safety</i>
                        </div>
                        <div class="industry-title">医疗健康</div>
                    </div>
                    <div class="industry-applications">
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            医学影像分析
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            药物研发
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            健康咨询
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            病历分析
                        </div>
                    </div>
                    <div class="case-study">
                        <div class="case-title">
                            <i class="material-icons case-icon">stars</i>
                            案例
                        </div>
                        <div class="case-content">
                            <div class="case-text">某医院使用大模型辅助诊断</div>
                            <div class="case-metric">诊断时间↓50%</div>
                        </div>
                    </div>
                </div>
                
                <div class="industry-card">
                    <div class="industry-header">
                        <div class="industry-icon">
                            <i class="material-icons">school</i>
                        </div>
                        <div class="industry-title">教育行业</div>
                    </div>
                    <div class="industry-applications">
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            个性化学习
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            智能辅导
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            作业批改
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            教育资源生成
                        </div>
                    </div>
                    <div class="case-study">
                        <div class="case-title">
                            <i class="material-icons case-icon">stars</i>
                            案例
                        </div>
                        <div class="case-content">
                            <div class="case-text">某教育平台提供个性化学习路径</div>
                            <div class="case-metric">学习效率↑40%</div>
                        </div>
                    </div>
                </div>
                
                <div class="industry-card">
                    <div class="industry-header">
                        <div class="industry-icon">
                            <i class="material-icons">precision_manufacturing</i>
                        </div>
                        <div class="industry-title">制造业</div>
                    </div>
                    <div class="industry-applications">
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            质量控制
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            设备预测性维护
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            生产优化
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            供应链管理
                        </div>
                    </div>
                    <div class="case-study">
                        <div class="case-title">
                            <i class="material-icons case-icon">stars</i>
                            案例
                        </div>
                        <div class="case-content">
                            <div class="case-text">某制造企业预测设备故障</div>
                            <div class="case-metric">停机时间↓60%</div>
                        </div>
                    </div>
                </div>
                
                <div class="industry-card">
                    <div class="industry-header">
                        <div class="industry-icon">
                            <i class="material-icons">shopping_cart</i>
                        </div>
                        <div class="industry-title">零售行业</div>
                    </div>
                    <div class="industry-applications">
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            智能推荐
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            需求预测
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            客户服务
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">check_circle</i>
                            营销文案生成
                        </div>
                    </div>
                    <div class="case-study">
                        <div class="case-title">
                            <i class="material-icons case-icon">stars</i>
                            案例
                        </div>
                        <div class="case-content">
                            <div class="case-text">某零售企业生成个性化营销文案</div>
                            <div class="case-metric">转化率↑25%</div>
                        </div>
                    </div>
                </div>
                
                <div class="industry-card">
                    <div class="industry-header">
                        <div class="industry-icon">
                            <i class="material-icons">integration_instructions</i>
                        </div>
                        <div class="industry-title">未来展望</div>
                    </div>
                    <div class="industry-applications">
                        <div class="application-item">
                            <i class="material-icons application-icon">trending_up</i>
                            <span class="highlight">跨行业融合</span>：打破行业壁垒
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">trending_up</i>
                            <span class="highlight">多模态应用</span>：文本、图像、语音结合
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">trending_up</i>
                            <span class="highlight">边缘计算</span>：本地化部署与实时响应
                        </div>
                        <div class="application-item">
                            <i class="material-icons application-icon">trending_up</i>
                            <span class="highlight">人机协作</span>：AI辅助而非替代人类
                        </div>
                    </div>
                    <div class="case-study" style="background-color: #f0f7ff;">
                        <div class="case-title" style="color: #4a6fa5;">
                            <i class="material-icons case-icon">lightbulb</i>
                            关键洞察
                        </div>
                        <div class="case-content">
                            <div class="case-text">大模型正从"通用助手"向"行业专家"转变</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第15页
        </div>
    </div>
</body>
</html>