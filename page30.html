<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词工程资源：实用工具与资源</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .resources-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            flex-grow: 1;
        }
        .resource-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .resource-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .resource-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .resource-icon-container {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .resource-icon {
            color: white;
            font-size: 24px;
        }
        .resource-title {
            font-size: 20px;
            font-weight: 700;
            color: #0066cc;
        }
        .resource-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .resource-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .resource-item {
            display: flex;
            align-items: flex-start;
            background-color: #f0f7ff;
            border-radius: 10px;
            padding: 10px;
            transition: all 0.3s ease;
        }
        .resource-item:hover {
            transform: translateX(5px);
            background-color: #e1f0ff;
        }
        .item-icon {
            color: #0066cc;
            font-size: 18px;
            margin-right: 10px;
            margin-top: 2px;
            flex-shrink: 0;
        }
        .item-content {
            flex-grow: 1;
        }
        .item-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 3px;
        }
        .item-desc {
            font-size: 14px;
            color: #666;
        }
        .equipment-visual {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: space-around;
        }
        .equipment-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }
        .equipment-item:hover {
            transform: scale(1.1);
        }
        .equipment-icon {
            font-size: 36px;
            color: #0066cc;
            margin-bottom: 8px;
        }
        .equipment-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">提示词工程资源：实用工具与资源</h1>
            
            <div class="resources-grid">
                <div class="resource-card">
                    <div class="resource-header">
                        <div class="resource-icon-container">
                            <i class="fas fa-tools resource-icon"></i>
                        </div>
                        <div class="resource-title">提示词工程工具</div>
                    </div>
                    <div class="resource-content">
                        <div class="resource-list">
                            <div class="resource-item">
                                <i class="fas fa-magic item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">提示词生成器</div>
                                    <div class="item-desc">PromptPerfect、PromptBase</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-cogs item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">提示词优化工具</div>
                                    <div class="item-desc">PromptOptimize</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-flask item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">提示词测试工具</div>
                                    <div class="item-desc">PromptLab</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-database item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">提示词库</div>
                                    <div class="item-desc">PromptHero、FlowGPT</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="resource-card">
                    <div class="resource-header">
                        <div class="resource-icon-container">
                            <i class="fas fa-book-open resource-icon"></i>
                        </div>
                        <div class="resource-title">学习资源</div>
                    </div>
                    <div class="resource-content">
                        <div class="resource-list">
                            <div class="resource-item">
                                <i class="fas fa-graduation-cap item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">在线课程</div>
                                    <div class="item-desc">Coursera"Prompt Engineering for ChatGPT"</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-book item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">书籍</div>
                                    <div class="item-desc">《The Art of Prompt Engineering》</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-blog item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">博客文章</div>
                                    <div class="item-desc">OpenAI官方博客提示工程指南</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-video item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">视频教程</div>
                                    <div class="item-desc">YouTube提示工程教程</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="resource-card">
                    <div class="resource-header">
                        <div class="resource-icon-container">
                            <i class="fas fa-users resource-icon"></i>
                        </div>
                        <div class="resource-title">社区资源</div>
                    </div>
                    <div class="resource-content">
                        <div class="resource-list">
                            <div class="resource-item">
                                <i class="fab fa-reddit item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">Reddit社区</div>
                                    <div class="item-desc">r/PromptEngineering</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fab fa-discord item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">Discord服务器</div>
                                    <div class="item-desc">提示工程专业社区</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fab fa-github item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">GitHub项目</div>
                                    <div class="item-desc">开源提示工程工具和资源</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-comments item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">论坛</div>
                                    <div class="item-desc">专业提示工程讨论区</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="resource-card">
                    <div class="resource-header">
                        <div class="resource-icon-container">
                            <i class="fas fa-building resource-icon"></i>
                        </div>
                        <div class="resource-title">企业内部资源</div>
                    </div>
                    <div class="resource-content">
                        <div class="resource-list">
                            <div class="resource-item">
                                <i class="fas fa-clipboard-list item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">提示词模板库</div>
                                    <div class="item-desc">公司内部常用提示词模板</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-clipboard-check item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">最佳实践指南</div>
                                    <div class="item-desc">企业提示工程标准流程</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-users-cog item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">提示词分享会</div>
                                    <div class="item-desc">内部经验交流活动</div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <i class="fas fa-chalkboard-teacher item-icon"></i>
                                <div class="item-content">
                                    <div class="item-name">培训课程</div>
                                    <div class="item-desc">企业内部提示工程培训</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="equipment-visual">
                <div class="equipment-item">
                    <i class="fas fa-toolbox equipment-icon"></i>
                    <div class="equipment-text">工具</div>
                </div>
                
                <i class="fas fa-plus text-blue-500 text-2xl"></i>
                
                <div class="equipment-item">
                    <i class="fas fa-book equipment-icon"></i>
                    <div class="equipment-text">知识</div>
                </div>
                
                <i class="fas fa-plus text-blue-500 text-2xl"></i>
                
                <div class="equipment-item">
                    <i class="fas fa-users equipment-icon"></i>
                    <div class="equipment-text">社区</div>
                </div>
                
                <i class="fas fa-equals text-blue-500 text-2xl"></i>
                
                <div class="equipment-item">
                    <i class="fas fa-rocket equipment-icon"></i>
                    <div class="equipment-text">提示词工程师</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第30页
        </div>
    </div>
</body>
</html>