<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级英雄联盟：MOE混合专家模型</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            padding: 40px 0;
        }
        .left-content {
            width: 70%;
            padding-left: 70px;
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            padding-top: 0;
        }
        .right-content {
            width: 50%;
            padding-left: 20px;
            padding-right: 70px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            padding-top: 0;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 30px;
            margin-top: 20px;
            color: #ffffff;
        }
        .feature-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .feature-icon {
            background-color: #764ba2;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .feature-title {
            font-size: 22px;
            font-weight: 600;
            color: #333;
        }
        .feature-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .highlight {
            color: #764ba2;
            font-weight: 600;
        }
        .moe-visual {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
            position: relative;
            margin-top: 94px;
        }
        .superhero-team {
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-bottom: 30px;
            height: 180px;
        }
        .superhero {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 8px;
            transition: transform 0.3s ease;
        }
        .superhero:hover {
            transform: translateY(-10px);
        }
        .hero-icon {
            font-size: 45px;
            color: #764ba2;
            margin-bottom: 10px;
        }
        .hero-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            text-align: center;
        }
        .captain {
            position: relative;
            top: -20px;
        }
        .captain .hero-icon {
            font-size: 55px;
            color: #ff9800;
        }
        .moe-components {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        .component {
            width: 48%;
            background-color: #f0f7ff;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        .component-icon {
            font-size: 40px;
            color: #764ba2;
            margin-bottom: 10px;
        }
        .component-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        .component-desc {
            font-size: 16px;
            color: #555;
        }
        .moe-advantage {
            margin-top: 20px;
            background-color: #f9f2ff;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
        }
        .advantage-title {
            font-size: 20px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 10px;
        }
        .advantage-desc {
            font-size: 18px;
            color: #333;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <div class="left-content">
                <h1 class="title">超级英雄联盟：MOE混合专家模型</h1>
                
                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="material-icons">speed</i>
                        </div>
                        <div class="feature-title">MOE的特点</div>
                    </div>
                    <div class="feature-content">
                        • 与稠密模型相比，<span class="highlight">预训练速度更快</span><br>
                        • 与相同参数模型相比，<span class="highlight">推理速度更快</span><br>
                        • 需要<span class="highlight">大量显存</span>，所有专家需加载到内存
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="material-icons">architecture</i>
                        </div>
                        <div class="feature-title">MOE的组成</div>
                    </div>
                    <div class="feature-content">
                        • <span class="highlight">稀疏MoE层</span>：代替传统Transformer的前馈网络<br>
                        • <span class="highlight">门控网络</span>：决定哪些token被发送到哪个专家
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-header">
                        <div class="feature-icon">
                            <i class="material-icons">trending_up</i>
                        </div>
                        <div class="feature-title">MOE的优势</div>
                    </div>
                    <div class="feature-content">
                        有限计算资源下，<span class="highlight">更少训练步数</span>训练<span class="highlight">更大模型</span>，效果优于更多步数训练较小模型
                    </div>
                </div>
            </div>
            
            <div class="right-content">
                <div class="moe-visual">
                    <div class="superhero-team">
                        <div class="superhero">
                            <i class="material-icons hero-icon">science</i>
                            <div class="hero-name">科学专家</div>
                        </div>
                        <div class="superhero">
                            <i class="material-icons hero-icon">calculate</i>
                            <div class="hero-name">数学专家</div>
                        </div>
                        <div class="superhero captain">
                            <i class="material-icons hero-icon">hub</i>
                            <div class="hero-name">门控网络<br>(队长)</div>
                        </div>
                        <div class="superhero">
                            <i class="material-icons hero-icon">language</i>
                            <div class="hero-name">语言专家</div>
                        </div>
                        <div class="superhero">
                            <i class="material-icons hero-icon">code</i>
                            <div class="hero-name">编程专家</div>
                        </div>
                    </div>
                    
                    <div class="moe-components">
                        <div class="component">
                            <i class="material-icons component-icon">layers</i>
                            <div class="component-title">稀疏MoE层</div>
                            <div class="component-desc">包含多个专家网络，每个专家都是独立的神经网络</div>
                        </div>
                        
                        <div class="component">
                            <i class="material-icons component-icon">router</i>
                            <div class="component-title">门控网络</div>
                            <div class="component-desc">决定哪些token被发送到哪个专家，像队长分配任务</div>
                        </div>
                    </div>
                    
                    <div class="moe-advantage">
                        <div class="advantage-title">超级英雄的效率</div>
                        <div class="advantage-desc">每个英雄只在自己擅长的领域出任务，整体效率更高！</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第6页
        </div>
    </div>
</body>
</html>