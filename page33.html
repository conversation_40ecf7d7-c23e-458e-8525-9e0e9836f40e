<!DOCTYPE html>
<html lang="zh-CN">
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>彩蛋揭秘：本PPT的诞生故事</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&amp;display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .main-container {
            display: flex;
            gap: 30px;
            flex-grow: 1;
        }
        .left-panel {
            width: 55%;
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: stretch;
        }
        .right-panel {
            width: 45%;
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: stretch;
        }
        .card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .card-icon {
            background-color: #ff8c00;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .card-title {
            font-size: 24px;
            font-weight: 700;
            color: #ff8c00;
        }
        .process-steps {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 10px;
        }
        .process-step {
            display: flex;
            align-items: flex-start;
            background-color: #fff8e6;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .process-step:hover {
            transform: translateX(10px);
            background-color: #ffeccc;
        }
        .step-number {
            width: 30px;
            height: 30px;
            background-color: #ff8c00;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: 700;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .step-content {
            flex-grow: 1;
        }
        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .step-desc {
            font-size: 16px;
            color: #666;
        }
        .techniques-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 10px;
        }
        .technique-item {
            background-color: #fff8e6;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
        }
        .technique-item:hover {
            transform: translateY(-5px);
            background-color: #ffeccc;
        }
        .technique-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .technique-icon {
            color: #ff8c00;
            font-size: 20px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        .technique-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .technique-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        .collab-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 20px;
        }
        .collab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }
        .collab-item:hover {
            transform: scale(1.1);
        }
        .collab-icon {
            font-size: 50px;
            margin-bottom: 10px;
        }
        .human-icon {
            color: #ff8c00;
        }
        .ai-icon {
            color: #4a90e2;
        }
        .result-icon {
            color: #50c878;
        }
        .collab-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .arrow-icon {
            color: #666;
            font-size: 30px;
        }
        .magic-reveal {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        .magic-text {
            font-size: 28px;
            font-weight: 700;
            color: #ff8c00;
            text-align: center;
            z-index: 2;
        }
        .magic-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(255, 140, 0, 0.1), rgba(74, 144, 226, 0.1));
            z-index: 1;
        }
        .sparkle {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #ff8c00;
            border-radius: 50%;
            opacity: 0.7;
            z-index: 1;
            animation: sparkle 2s infinite;
        }
        @keyframes sparkle {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.5); opacity: 1; }
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">彩蛋揭秘：本PPT的诞生故事</h1>
            
            <div class="main-container">
                <div class="left-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-magic"></i>
                            </div>
                            <div class="card-title">创作过程</div>
                        </div>
                        
                        <div class="process-steps">
                            <div class="process-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <div class="step-title">确定培训大纲</div>
                                    <div class="step-desc">明确培训目标、受众和内容要点</div>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <div class="step-title">设计提示词</div>
                                    <div class="step-desc">精心设计引导AI生成内容的提示词</div>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <div class="step-title">AI生成初稿</div>
                                    <div class="step-desc">大语言模型根据提示词生成内容初稿</div>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <div class="step-title">人工优化调整</div>
                                    <div class="step-desc">专业人员对内容进行优化和调整</div>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-number">5</div>
                                <div class="step-content">
                                    <div class="step-title">整合成完整PPT</div>
                                    <div class="step-desc">将优化后的内容整合成最终PPT</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="card-title">使用的提示词技巧</div>
                        </div>
                        
                        <div class="techniques-grid">
                            <div class="technique-item">
                                <div class="technique-header">
                                    <i class="fas fa-user-tag technique-icon"></i>
                                    <div class="technique-title">角色设定</div>
                                </div>
                                <div class="technique-desc">
                                    "你是一位专业的培训设计师，请为全员AI培训设计内容..."
                                </div>
                            </div>
                            
                            <div class="technique-item">
                                <div class="technique-header">
                                    <i class="fas fa-info-circle technique-icon"></i>
                                    <div class="technique-title">上下文提供</div>
                                </div>
                                <div class="technique-desc">
                                    "这是一个面向全体员工的AI培训，内容包括大语言模型基础..."
                                </div>
                            </div>
                            
                            <div class="technique-item">
                                <div class="technique-header">
                                    <i class="fas fa-file-alt technique-icon"></i>
                                    <div class="technique-title">示例引导</div>
                                </div>
                                <div class="technique-desc">
                                    "请按照以下风格和格式编写内容：诙谐幽默的语言、生动的比喻..."
                                </div>
                            </div>
                            
                            <div class="technique-item">
                                <div class="technique-header">
                                    <i class="fas fa-tasks technique-icon"></i>
                                    <div class="technique-title">分步思考</div>
                                </div>
                                <div class="technique-desc">
                                    "请先分析内容要点，再组织成PPT页面，最后添加视觉元素..."
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="right-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <div class="card-title">人机协作的价值</div>
                        </div>
                        
                        <div class="process-steps">
                            <div class="process-step">
                                <div class="step-number">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="step-content">
                                    <div class="step-title">人类优势</div>
                                    <div class="step-desc">专业指导、最终决策、创意方向、质量控制</div>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-number">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="step-content">
                                    <div class="step-title">AI优势</div>
                                    <div class="step-desc">内容生成、创意初稿、知识整合、效率提升</div>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-number">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="step-content">
                                    <div class="step-title">协作效果</div>
                                    <div class="step-desc">1+1&gt;2，实现人机优势互补，创作出更优质的培训材料</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="collab-visual">
                            <div class="collab-item">
                                <i class="fas fa-user-tie collab-icon human-icon"></i>
                                <div class="collab-text">人类专家</div>
                            </div>
                            
                            <i class="fas fa-plus arrow-icon"></i>
                            
                            <div class="collab-item">
                                <i class="fas fa-robot collab-icon ai-icon"></i>
                                <div class="collab-text">AI助手</div>
                            </div>
                            
                            <i class="fas fa-equals arrow-icon"></i>
                            
                            <div class="collab-item">
                                <i class="fas fa-file-powerpoint collab-icon result-icon"></i>
                                <div class="collab-text">优质PPT</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-surprise"></i>
                            </div>
                            <div class="card-title">惊喜揭示</div>
                        </div>
                        
                        <div class="flex flex-col items-center justify-center h-full">
                            <div class="text-5xl font-bold text-center mb-6 text-orange-600">
                                本PPT正是通过<br>提示词工程与大语言模型<br>协作创作的！
                            </div>
                            
                            <div class="text-2xl font-semibold text-center mb-8 text-gray-700">
                                这就是提示词工程的魔力！
                            </div>
                            
                            <div class="text-xl font-medium text-center text-gray-600">
                                感谢您的参与！
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="magic-reveal">
                <div class="magic-bg"></div>
                <!-- Adding sparkle effects -->
                <div class="sparkle" style="top: 20%; left: 10%;"></div>
                <div class="sparkle" style="top: 30%; left: 80%;"></div>
                <div class="sparkle" style="top: 70%; left: 20%;"></div>
                <div class="sparkle" style="top: 80%; left: 70%;"></div>
                <div class="sparkle" style="top: 50%; left: 50%;"></div>
                
                <div class="magic-text">
                    <i class="fas fa-magic mr-3"></i>
                    提示词工程让创意无限可能
                    <i class="fas fa-magic ml-3"></i>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第33页
        </div>
    </div>

</body></html>