<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大语言模型的'语言'：Prompt、分词器与Token</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            padding: 40px 0;
        }
        .left-content {
            width: 50%;
            padding-left: 70px;
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .right-content {
            width: 50%;
            padding-left: 20px;
            padding-right: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 40px;
            color: #8B4513;
        }
        .concept-card {
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .concept-card:hover {
            transform: translateY(-5px);
        }
        .concept-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .concept-icon {
            background-color: #FF8C00;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .concept-title {
            font-size: 22px;
            font-weight: 600;
            color: #8B4513;
        }
        .concept-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .highlight {
            color: #FF8C00;
            font-weight: 600;
        }
        .token-visual {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 500px;
        }
        .token-example {
            margin-bottom: 30px;
        }
        .example-title {
            font-size: 22px;
            font-weight: 600;
            color: #8B4513;
            margin-bottom: 15px;
            text-align: center;
        }
        .example-sentence {
            font-size: 20px;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #FFF8DC;
            border-radius: 10px;
        }
        .token-breakdown {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        .token-block {
            background-color: #FFE4B5;
            border: 2px solid #FF8C00;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 18px;
            font-weight: 500;
            color: #8B4513;
        }
        .metaphor-container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        .metaphor-item {
            width: 30%;
            text-align: center;
        }
        .metaphor-icon {
            font-size: 40px;
            color: #FF8C00;
            margin-bottom: 10px;
        }
        .metaphor-title {
            font-size: 18px;
            font-weight: 600;
            color: #8B4513;
            margin-bottom: 5px;
        }
        .metaphor-desc {
            font-size: 16px;
            color: #555;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(139, 69, 19, 0.7);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <div class="left-content">
                <h1 class="title">大语言模型的'语言'：Prompt、分词器与Token</h1>
                
                <div class="concept-card">
                    <div class="concept-header">
                        <div class="concept-icon">
                            <i class="material-icons">description</i>
                        </div>
                        <div class="concept-title">Prompt</div>
                    </div>
                    <div class="concept-content">
                        给模型的<span class="highlight">指令</span>或<span class="highlight">提示</span>，就像告诉图书管理员你要找什么书
                    </div>
                </div>
                
                <div class="concept-card">
                    <div class="concept-header">
                        <div class="concept-icon">
                            <i class="material-icons">content_cut</i>
                        </div>
                        <div class="concept-title">分词器</div>
                    </div>
                    <div class="concept-content">
                        将文本<span class="highlight">拆分成更小单元</span>的工具，就像将句子拆分成单词的工具
                    </div>
                </div>
                
                <div class="concept-card">
                    <div class="concept-header">
                        <div class="concept-icon">
                            <i class="material-icons">extension</i>
                        </div>
                        <div class="concept-title">Token</div>
                    </div>
                    <div class="concept-content">
                        大语言模型处理的<span class="highlight">最小单元</span>，可以是单词、字符、子词或符号，就像语言的基本积木
                    </div>
                </div>
            </div>
            
            <div class="right-content">
                <div class="token-visual">
                    <div class="token-example">
                        <div class="example-title">Token 示例</div>
                        <div class="example-sentence">我喜欢吃苹果</div>
                        <div class="token-breakdown">
                            <div class="token-block">我</div>
                            <div class="token-block">喜欢</div>
                            <div class="token-block">吃</div>
                            <div class="token-block">苹果</div>
                        </div>
                    </div>
                    
                    <div class="metaphor-container">
                        <div class="metaphor-item">
                            <i class="material-icons metaphor-icon">menu_book</i>
                            <div class="metaphor-title">Prompt</div>
                            <div class="metaphor-desc">搭建积木的说明书</div>
                        </div>
                        
                        <div class="metaphor-item">
                            <i class="material-icons metaphor-icon">build</i>
                            <div class="metaphor-title">分词器</div>
                            <div class="metaphor-desc">拆积木的工具</div>
                        </div>
                        
                        <div class="metaphor-item">
                            <i class="material-icons metaphor-icon">widgets</i>
                            <div class="metaphor-title">Token</div>
                            <div class="metaphor-desc">字母积木</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第4页
        </div>
    </div>
</body>
</html>