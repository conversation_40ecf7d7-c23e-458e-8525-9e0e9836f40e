<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大模型的'变身术'：应用开发流程</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 70px;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
        }
        .process-container {
            display: flex;
            flex-grow: 1;
            gap: 25px;
        }
        .process-steps {
            width: 60%;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .process-visual {
            width: 40%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .step-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
        }
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .step-number {
            width: 40px;
            height: 40px;
            background-color: #764ba2;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            font-weight: 700;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .step-content {
            flex-grow: 1;
        }
        .step-title {
            font-size: 22px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        .step-icon {
            margin-right: 10px;
            font-size: 24px;
        }
        .step-desc {
            font-size: 18px;
            color: #333;
        }
        .highlight {
            color: #764ba2;
            font-weight: 600;
        }
        .transformation-visual {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .transformation-title {
            font-size: 24px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 25px;
            text-align: center;
        }
        .transformation-stages {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .stage {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 12px;
            padding: 15px;
            transition: transform 0.3s ease;
        }
        .stage:hover {
            transform: scale(1.05);
            background-color: #f0f7ff;
        }
        .stage-icon {
            width: 50px;
            height: 50px;
            background-color: #764ba2;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .stage-text {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }
        .arrow-icon {
            color: #764ba2;
            font-size: 30px;
            margin: 0 auto;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">大模型的'变身术'：应用开发流程</h1>
            
            <div class="process-container">
                <div class="process-steps">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">
                                <i class="material-icons step-icon">lightbulb</i>
                                需求分析
                            </div>
                            <div class="step-desc">
                                明确业务场景和用户需求，确定大模型应用的<span class="highlight">目标</span>和<span class="highlight">价值</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">
                                <i class="material-icons step-icon">model_training</i>
                                模型选择
                            </div>
                            <div class="step-desc">
                                根据需求选择合适的大模型（<span class="highlight">开源或闭源</span>、<span class="highlight">通用或专用</span>、<span class="highlight">大参数或小参数</span>等）
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <div class="step-title">
                                <i class="material-icons step-icon">psychology</i>
                                提示工程
                            </div>
                            <div class="step-desc">
                                设计<span class="highlight">有效的提示词</span>，引导模型产生期望的输出
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-card">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <div class="step-title">
                                <i class="material-icons step-icon">tune</i>
                                微调
                            </div>
                            <div class="step-desc">
                                使用<span class="highlight">特定领域数据</span>对模型进行微调，提升模型在特定任务上的表现
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-card">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <div class="step-title">
                                <i class="material-icons step-icon">integration_instructions</i>
                                系统集成
                            </div>
                            <div class="step-desc">
                                将大模型<span class="highlight">集成到现有系统</span>中，设计用户界面和交互流程
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-card">
                        <div class="step-number">6</div>
                        <div class="step-content">
                            <div class="step-title">
                                <i class="material-icons step-icon">speed</i>
                                测试与优化
                            </div>
                            <div class="step-desc">
                                <span class="highlight">测试应用效果</span>，收集用户反馈，持续优化模型和系统
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="process-visual">
                    <div class="transformation-visual">
                        <div class="transformation-title">大模型的"变形记"</div>
                        
                        <div class="transformation-stages">
                            <div class="stage">
                                <div class="stage-icon">
                                    <i class="material-icons">smart_toy</i>
                                </div>
                                <div class="stage-text">通用大模型</div>
                            </div>
                            
                            <i class="material-icons arrow-icon">arrow_downward</i>
                            
                            <div class="stage">
                                <div class="stage-icon">
                                    <i class="material-icons">settings</i>
                                </div>
                                <div class="stage-text">需求分析与选择</div>
                            </div>
                            
                            <i class="material-icons arrow-icon">arrow_downward</i>
                            
                            <div class="stage">
                                <div class="stage-icon">
                                    <i class="material-icons">psychology_alt</i>
                                </div>
                                <div class="stage-text">提示工程与微调</div>
                            </div>
                            
                            <i class="material-icons arrow-icon">arrow_downward</i>
                            
                            <div class="stage">
                                <div class="stage-icon">
                                    <i class="material-icons">workspace_premium</i>
                                </div>
                                <div class="stage-text">特定领域专家</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第16页
        </div>
    </div>
</body>
</html>