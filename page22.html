<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI时代的'职业新地图'：职业发展路径</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .career-container {
            display: flex;
            gap: 25px;
            flex-grow: 1;
        }
        .career-left {
            width: 60%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .career-right {
            width: 40%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .career-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .career-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .card-icon {
            background-color: #f5576c;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .card-title {
            font-size: 22px;
            font-weight: 700;
            color: #333;
        }
        .job-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 5px;
        }
        .job-item {
            background-color: #fff0f3;
            border-radius: 12px;
            padding: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }
        .job-item:hover {
            transform: scale(1.05);
            background-color: #ffe0e6;
        }
        .job-icon {
            color: #f5576c;
            font-size: 20px;
            margin-right: 10px;
        }
        .job-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .transformation-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 5px;
        }
        .transformation-item {
            display: flex;
            align-items: center;
            background-color: #fff0f3;
            border-radius: 12px;
            padding: 12px;
            transition: all 0.3s ease;
        }
        .transformation-item:hover {
            transform: translateX(10px);
            background-color: #ffe0e6;
        }
        .transformation-from {
            font-size: 15px;
            color: #666;
            text-decoration: line-through;
            margin-right: 10px;
            flex-shrink: 0;
            width: 120px;
            text-align: right;
        }
        .transformation-arrow {
            color: #f5576c;
            margin: 0 10px;
            flex-shrink: 0;
        }
        .transformation-to {
            font-size: 16px;
            font-weight: 600;
            color: #f5576c;
        }
        .strategy-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 5px;
        }
        .strategy-item {
            display: flex;
            align-items: flex-start;
            background-color: #fff0f3;
            border-radius: 12px;
            padding: 12px;
            transition: all 0.3s ease;
        }
        .strategy-item:hover {
            transform: translateY(-5px);
            background-color: #ffe0e6;
        }
        .strategy-icon {
            color: #f5576c;
            font-size: 20px;
            margin-right: 10px;
            margin-top: 2px;
            flex-shrink: 0;
        }
        .strategy-content {
            flex-grow: 1;
        }
        .strategy-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .strategy-desc {
            font-size: 14px;
            color: #666;
        }
        .competency-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 5px;
        }
        .competency-item {
            background-color: #fff0f3;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }
        .competency-item:hover {
            transform: translateY(-5px);
            background-color: #ffe0e6;
        }
        .competency-icon {
            width: 40px;
            height: 40px;
            background-color: #f5576c;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
        }
        .competency-icon i {
            color: white;
            font-size: 20px;
        }
        .competency-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .competency-desc {
            font-size: 14px;
            color: #666;
        }
        .exploration-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 15px;
        }
        .exploration-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }
        .exploration-item:hover {
            transform: scale(1.05);
        }
        .exploration-icon {
            font-size: 30px;
            color: #f5576c;
            margin-bottom: 8px;
        }
        .exploration-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">AI时代的'职业新地图'：职业发展路径</h1>
            
            <div class="career-container">
                <div class="career-left">
                    <div class="career-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="card-title">AI创造的新职业</div>
                        </div>
                        
                        <div class="job-grid">
                            <div class="job-item">
                                <i class="fas fa-comment-dots job-icon"></i>
                                <div class="job-title">提示工程师</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-robot job-icon"></i>
                                <div class="job-title">AI训练师</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-balance-scale job-icon"></i>
                                <div class="job-title">AI伦理专家</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-cube job-icon"></i>
                                <div class="job-title">AI产品经理</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-sitemap job-icon"></i>
                                <div class="job-title">AI系统架构师</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-shield-alt job-icon"></i>
                                <div class="job-title">AI安全专家</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-brain job-icon"></i>
                                <div class="job-title">机器学习工程师</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-chart-line job-icon"></i>
                                <div class="job-title">AI数据标注师</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-user-tie job-icon"></i>
                                <div class="job-title">AI业务顾问</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-cogs job-icon"></i>
                                <div class="job-title">AI运维工程师</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-microscope job-icon"></i>
                                <div class="job-title">AI研究员</div>
                            </div>

                            <div class="job-item">
                                <i class="fas fa-handshake job-icon"></i>
                                <div class="job-title">人机交互设计师</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="career-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="card-title">传统职业的转型</div>
                        </div>
                        
                        <div class="transformation-list">
                            <div class="transformation-item">
                                <div class="transformation-from">内容创作者</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI内容策略师</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">数据分析师</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI数据科学家</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">客服代表</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI客户体验设计师</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">教师</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI教育设计师</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">程序员</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI辅助开发工程师</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">医生</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI医疗诊断专家</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">律师</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI法律分析师</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">财务分析师</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI金融风控专家</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">设计师</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI创意总监</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">销售代表</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI销售策略师</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">HR专员</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI人才发展顾问</div>
                            </div>

                            <div class="transformation-item">
                                <div class="transformation-from">市场营销</div>
                                <i class="fas fa-arrow-right transformation-arrow"></i>
                                <div class="transformation-to">AI营销自动化专家</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="career-right">
                    <div class="career-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-chess"></i>
                            </div>
                            <div class="card-title">职业发展策略</div>
                        </div>
                        
                        <div class="strategy-list">
                            <div class="strategy-item">
                                <i class="fas fa-graduation-cap strategy-icon"></i>
                                <div class="strategy-content">
                                    <div class="strategy-title">持续学习AI知识</div>
                                    <div class="strategy-desc">掌握基础概念、工具和应用场景</div>
                                </div>
                            </div>
                            
                            <div class="strategy-item">
                                <i class="fas fa-project-diagram strategy-icon"></i>
                                <div class="strategy-content">
                                    <div class="strategy-title">积极参与AI项目</div>
                                    <div class="strategy-desc">在实践中积累经验和案例</div>
                                </div>
                            </div>
                            
                            <div class="strategy-item">
                                <i class="fas fa-network-wired strategy-icon"></i>
                                <div class="strategy-content">
                                    <div class="strategy-title">建立跨领域知识体系</div>
                                    <div class="strategy-desc">结合专业领域知识与AI技能</div>
                                </div>
                            </div>
                            
                            <div class="strategy-item">
                                <i class="fas fa-star strategy-icon"></i>
                                <div class="strategy-content">
                                    <div class="strategy-title">培养独特人类优势</div>
                                    <div class="strategy-desc">创造力、情感智能、伦理判断</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="career-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="card-title">未来职场竞争力</div>
                        </div>
                        
                        <div class="competency-grid">
                            <div class="competency-item">
                                <div class="competency-icon">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="competency-title">AI素养</div>
                                <div class="competency-desc">理解AI原理与应用</div>
                            </div>
                            
                            <div class="competency-item">
                                <div class="competency-icon">
                                    <i class="fas fa-hands-helping"></i>
                                </div>
                                <div class="competency-title">人机协作能力</div>
                                <div class="competency-desc">有效与AI协同工作</div>
                            </div>
                            
                            <div class="competency-item">
                                <div class="competency-icon">
                                    <i class="fas fa-lightbulb"></i>
                                </div>
                                <div class="competency-title">创新思维</div>
                                <div class="competency-desc">突破常规解决问题</div>
                            </div>
                            
                            <div class="competency-item">
                                <div class="competency-icon">
                                    <i class="fas fa-sync-alt"></i>
                                </div>
                                <div class="competency-title">适应变化能力</div>
                                <div class="competency-desc">快速学习新技术</div>
                            </div>
                        </div>
                        
                        <div class="exploration-visual">
                            <div class="exploration-item">
                                <i class="fas fa-compass exploration-icon"></i>
                                <div class="exploration-text">探索</div>
                            </div>
                            
                            <div class="exploration-item">
                                <i class="fas fa-map-marked-alt exploration-icon"></i>
                                <div class="exploration-text">定位</div>
                            </div>
                            
                            <div class="exploration-item">
                                <i class="fas fa-route exploration-icon"></i>
                                <div class="exploration-text">规划</div>
                            </div>
                            
                            <div class="exploration-item">
                                <i class="fas fa-flag-checkered exploration-icon"></i>
                                <div class="exploration-text">成功</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第22页
        </div>
    </div>
</body>
</html>