<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大语言模型的'超能力'：RAG、Function Calling、MCP与Agent</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 70px;
            justify-content: center;
        }
        .cards-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 40px;
            color: #7b1fa2;
        }
        .ability-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-height: 200px;
        }
        .ability-card:hover {
            transform: translateY(-5px);
        }
        .ability-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 15px;
        }
        .ability-icon {
            color: #7b1fa2;
            font-size: 60px;
            margin-bottom: 10px;
        }
        .ability-title {
            font-size: 22px;
            font-weight: 600;
            color: #7b1fa2;
            margin-bottom: 5px;
        }
        .ability-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 15px;
            font-style: italic;
        }
        .ability-content {
            font-size: 18px;
            color: #333;
            line-height: 1.5;
        }
        .highlight {
            color: #7b1fa2;
            font-weight: 600;
        }

        .footer {
            padding: 20px 70px;
            color: rgba(123, 31, 162, 0.7);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">大语言模型的'超能力'：RAG、Function Calling、MCP与Agent</h1>

            <div class="cards-container">
                <div class="ability-card">
                    <div class="ability-header">
                        <i class="material-icons ability-icon">library_books</i>
                        <div class="ability-title">RAG</div>
                        <div class="ability-subtitle">知识图书馆</div>
                    </div>
                    <div class="ability-content">
                        检索增强生成，通过<span class="highlight">外部知识库</span>检索相关信息，增强模型回答的准确性和时效性
                    </div>
                </div>

                <div class="ability-card">
                    <div class="ability-header">
                        <i class="material-icons ability-icon">handyman</i>
                        <div class="ability-title">Function Calling</div>
                        <div class="ability-subtitle">魔法工具箱</div>
                    </div>
                    <div class="ability-content">
                        大语言模型与<span class="highlight">外部工具</span>的交互模式，可调用函数获取实时数据、执行数据库查询等
                    </div>
                </div>

                <div class="ability-card">
                    <div class="ability-header">
                        <i class="material-icons ability-icon">support_agent</i>
                        <div class="ability-title">Agent</div>
                        <div class="ability-subtitle">超级管家</div>
                    </div>
                    <div class="ability-content">
                        负责根据用户输入<span class="highlight">动态决策</span>是否调用外部工具，像"智能助手"自主决策和执行任务
                    </div>
                </div>

                <div class="ability-card">
                    <div class="ability-header">
                        <i class="material-icons ability-icon">settings_input_hdmi</i>
                        <div class="ability-title">MCP</div>
                        <div class="ability-subtitle">万能接口</div>
                    </div>
                    <div class="ability-content">
                        连接【工具 + 数据】与【AI模型】之间的<span class="highlight">标准化协议</span>，像USB-C统一设备连接方式
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            全员AI培训系列 | 第10页
        </div>
    </div>
</body>
</html>