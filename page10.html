<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大语言模型的'超能力'：RAG、Function Calling、MCP与Agent</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            padding: 40px 0;
        }
        .left-content {
            width: 50%;
            padding-left: 70px;
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .right-content {
            width: 50%;
            padding-left: 20px;
            padding-right: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 40px;
            color: #7b1fa2;
        }
        .ability-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .ability-card:hover {
            transform: translateY(-5px);
        }
        .ability-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .ability-icon {
            background-color: #7b1fa2;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .ability-title {
            font-size: 22px;
            font-weight: 600;
            color: #7b1fa2;
        }
        .ability-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .highlight {
            color: #7b1fa2;
            font-weight: 600;
        }
        .superhero-visual {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 500px;
        }
        .superhero-team {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 10px;
        }
        .superhero {
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: transform 0.3s ease;
            flex: 1;
            min-width: 100px;
        }
        .superhero:hover {
            transform: translateY(-10px);
        }
        .hero-icon {
            font-size: 50px;
            color: #7b1fa2;
            margin-bottom: 10px;
        }
        .hero-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 5px;
        }
        .hero-desc {
            font-size: 14px;
            color: #666;
            text-align: center;
            max-width: 100px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .comparison-table th, .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        .comparison-table th {
            font-size: 18px;
            font-weight: 600;
            color: #7b1fa2;
            background-color: #f3e5f5;
        }
        .comparison-table td {
            font-size: 16px;
            color: #333;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(123, 31, 162, 0.7);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <div class="left-content">
                <h1 class="title">大语言模型的'超能力'：RAG、Function Calling、MCP与Agent</h1>
                
                <div class="ability-card">
                    <div class="ability-header">
                        <div class="ability-icon">
                            <i class="material-icons">search</i>
                        </div>
                        <div class="ability-title">RAG</div>
                    </div>
                    <div class="ability-content">
                        检索增强生成，通过<span class="highlight">外部知识库</span>检索相关信息，增强模型回答的准确性和时效性
                    </div>
                </div>

                <div class="ability-card">
                    <div class="ability-header">
                        <div class="ability-icon">
                            <i class="material-icons">build</i>
                        </div>
                        <div class="ability-title">Function Calling</div>
                    </div>
                    <div class="ability-content">
                        大语言模型与<span class="highlight">外部工具</span>的交互模式，可调用函数获取实时数据、执行数据库查询等
                    </div>
                </div>
                
                <div class="ability-card">
                    <div class="ability-header">
                        <div class="ability-icon">
                            <i class="material-icons">smart_toy</i>
                        </div>
                        <div class="ability-title">Agent</div>
                    </div>
                    <div class="ability-content">
                        负责根据用户输入<span class="highlight">动态决策</span>是否调用外部工具，像"智能助手"自主决策和执行任务
                    </div>
                </div>
                
                <div class="ability-card">
                    <div class="ability-header">
                        <div class="ability-icon">
                            <i class="material-icons">usb</i>
                        </div>
                        <div class="ability-title">MCP</div>
                    </div>
                    <div class="ability-content">
                        连接【工具 + 数据】与【AI模型】之间的<span class="highlight">标准化协议</span>，像USB-C统一设备连接方式
                    </div>
                </div>
            </div>
            
            <div class="right-content">
                <div class="superhero-visual">
                    <div class="superhero-team">
                        <div class="superhero">
                            <i class="material-icons hero-icon">library_books</i>
                            <div class="hero-name">RAG</div>
                            <div class="hero-desc">知识图书馆</div>
                        </div>

                        <div class="superhero">
                            <i class="material-icons hero-icon">handyman</i>
                            <div class="hero-name">Function Calling</div>
                            <div class="hero-desc">魔法工具箱</div>
                        </div>

                        <div class="superhero">
                            <i class="material-icons hero-icon">support_agent</i>
                            <div class="hero-name">Agent</div>
                            <div class="hero-desc">超级管家</div>
                        </div>

                        <div class="superhero">
                            <i class="material-icons hero-icon">settings_input_hdmi</i>
                            <div class="hero-name">MCP</div>
                            <div class="hero-desc">万能接口</div>
                        </div>
                    </div>
                    
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>特性</th>
                                <th>Function Calling</th>
                                <th>MCP</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>结构</strong></td>
                                <td>单体式模式</td>
                                <td>Client-Server架构</td>
                            </tr>
                            <tr>
                                <td><strong>扩展性</strong></td>
                                <td>紧耦合设计</td>
                                <td>松耦合设计</td>
                            </tr>
                            <tr>
                                <td><strong>适用场景</strong></td>
                                <td>轻量任务</td>
                                <td>企业级系统</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第10页
        </div>
    </div>
</body>
</html>