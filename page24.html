<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI时代的'行动指南'：总结与建议</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .main-container {
            display: flex;
            gap: 25px;
            flex-grow: 1;
        }
        .left-panel {
            width: 60%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .right-panel {
            width: 40%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .card-icon {
            background-color: #764ba2;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .card-title {
            font-size: 24px;
            font-weight: 700;
            color: #764ba2;
        }
        .review-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 10px;
        }
        .review-item {
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }
        .review-item:hover {
            transform: translateY(-5px);
            background-color: #f0e6ff;
        }
        .review-icon {
            width: 50px;
            height: 50px;
            background-color: #764ba2;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
        }
        .review-icon i {
            color: white;
            font-size: 24px;
        }
        .review-title {
            font-size: 18px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 5px;
        }
        .review-desc {
            font-size: 14px;
            color: #666;
        }
        .action-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 10px;
        }
        .action-item {
            display: flex;
            align-items: center;
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .action-item:hover {
            transform: translateX(10px);
            background-color: #f0e6ff;
        }
        .action-icon-container {
            width: 40px;
            height: 40px;
            background-color: #764ba2;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .action-icon {
            color: white;
            font-size: 20px;
        }
        .action-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .path-steps {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        .path-step {
            width: 23%;
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
        }
        .path-step:hover {
            transform: translateY(-5px);
            background-color: #f0e6ff;
        }
        .step-number {
            width: 30px;
            height: 30px;
            background-color: #764ba2;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .step-title {
            font-size: 16px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 5px;
        }
        .step-desc {
            font-size: 14px;
            color: #666;
        }
        .support-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 10px;
        }
        .support-item {
            display: flex;
            align-items: center;
            background-color: #f8f5ff;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .support-item:hover {
            transform: translateX(10px);
            background-color: #f0e6ff;
        }
        .support-icon {
            color: #764ba2;
            font-size: 20px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .support-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .conclusion {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .conclusion-text {
            font-size: 24px;
            font-weight: 700;
            color: #764ba2;
            text-align: center;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">AI时代的'行动指南'：总结与建议</h1>
            
            <div class="main-container">
                <div class="left-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="card-title">培训内容回顾</div>
                        </div>
                        
                        <div class="review-grid">
                            <div class="review-item">
                                <div class="review-icon">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="review-title">大语言模型基础</div>
                                <div class="review-desc">概念、原理、应用</div>
                            </div>
                            
                            <div class="review-item">
                                <div class="review-icon">
                                    <i class="fas fa-network-wired"></i>
                                </div>
                                <div class="review-title">大模型生态与应用</div>
                                <div class="review-desc">生态、场景、案例</div>
                            </div>
                            
                            <div class="review-item">
                                <div class="review-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <div class="review-title">工作思维转变</div>
                                <div class="review-desc">技能、方法、发展</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-compass"></i>
                            </div>
                            <div class="card-title">关键行动建议</div>
                        </div>
                        
                        <div class="action-list">
                            <div class="action-item">
                                <div class="action-icon-container">
                                    <i class="fas fa-graduation-cap action-icon"></i>
                                </div>
                                <div class="action-text">主动学习AI知识</div>
                            </div>
                            
                            <div class="action-item">
                                <div class="action-icon-container">
                                    <i class="fas fa-tools action-icon"></i>
                                </div>
                                <div class="action-text">尝试使用AI工具提高工作效率</div>
                            </div>
                            
                            <div class="action-item">
                                <div class="action-icon-container">
                                    <i class="fas fa-project-diagram action-icon"></i>
                                </div>
                                <div class="action-text">参与AI项目实践</div>
                            </div>
                            
                            <div class="action-item">
                                <div class="action-icon-container">
                                    <i class="fas fa-balance-scale action-icon"></i>
                                </div>
                                <div class="action-text">关注AI伦理与责任</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="right-panel">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="card-title">个人发展路径</div>
                        </div>
                        
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-number">1</div>
                                <div class="step-title">评估AI素养</div>
                                <div class="step-desc">了解自身起点</div>
                            </div>
                            
                            <div class="path-step">
                                <div class="step-number">2</div>
                                <div class="step-title">制定学习计划</div>
                                <div class="step-desc">明确学习目标</div>
                            </div>
                            
                            <div class="path-step">
                                <div class="step-number">3</div>
                                <div class="step-title">寻找实践机会</div>
                                <div class="step-desc">应用所学知识</div>
                            </div>
                            
                            <div class="path-step">
                                <div class="step-number">4</div>
                                <div class="step-title">建立学习社区</div>
                                <div class="step-desc">共同成长进步</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="card-title">企业支持措施</div>
                        </div>
                        
                        <div class="support-list">
                            <div class="support-item">
                                <i class="fas fa-book-open support-icon"></i>
                                <div class="support-text">提供AI培训资源</div>
                            </div>
                            
                            <div class="support-item">
                                <i class="fas fa-lightbulb support-icon"></i>
                                <div class="support-text">鼓励AI创新应用</div>
                            </div>
                            
                            <div class="support-item">
                                <i class="fas fa-gavel support-icon"></i>
                                <div class="support-text">建立AI伦理规范</div>
                            </div>
                            
                            <div class="support-item">
                                <i class="fas fa-users support-icon"></i>
                                <div class="support-text">营造AI友好文化</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="conclusion">
                <div class="conclusion-text">
                    <i class="fas fa-rocket mr-3"></i>
                    让我们一起拥抱AI时代，共创美好未来
                    <i class="fas fa-rocket ml-3"></i>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第24页
        </div>
    </div>
</body>
</html>