<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI基础培训系列：大语言模型入门指南</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            height: 720px;
            position: relative;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            margin: auto;
        }
        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            z-index: 1;
        }
        .content {
            position: relative;
            z-index: 2;
            display: flex;
            height: 100%;
            color: white;
        }
        .left-content {
            width: 60%;
            padding-left: 70px;
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            padding-top: 180px;
        }
        .right-content {
            width: 40%;
            padding-left: 20px;
            padding-right: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .title {
            font-size: 60px;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.2;
            color: #ffffff;
        }
        .subtitle {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 30px;
            color: #e0e0ff;
        }
        .robot-image {
            max-width: 90%;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: float 3s ease-in-out infinite;
        }
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
            100% { transform: translateY(0px); }
        }
        .keywords {
            margin-top: 50px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .keyword {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 30px;
            font-size: 18px;
            font-weight: 500;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .footer {
            position: absolute;
            bottom: 30px;
            left: 0;
            width: 100%;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            z-index: 2;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="overlay"></div>
        <div class="content">
            <div class="left-content">
                <h1 class="title">AI基础培训系列：<br>大语言模型入门指南</h1>
                <p class="subtitle">一起探索大模型世界的奥秘</p>
                <div class="keywords">
                    <div class="keyword">AIGC</div>
                    <div class="keyword">RAG</div>
                    <div class="keyword">Function Calling</div>
                    <div class="keyword">Agent</div>
                    <div class="keyword">MCP</div>
                </div>
            </div>
            <div class="right-content">
                <img src="https://sfile.chatglm.cn/images-ppt/048cbdc585b9.jpg" alt="可爱的AI机器人" class="robot-image">
            </div>
        </div>
        <div class="footer">
            2025年全员AI培训系列 | AI全功能团队出品
        </div>
    </div>
</body>
</html>