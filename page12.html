<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI的未来：从理解到创造</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            padding: 40px 0;
        }
        .left-content {
            width: 50%;
            padding-left: 70px;
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .right-content {
            width: 50%;
            padding-left: 20px;
            padding-right: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 40px;
            color: #ffffff;
        }
        .summary-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
        }
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .summary-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .summary-icon {
            background-color: #764ba2;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .summary-title {
            font-size: 22px;
            font-weight: 600;
            color: #333;
        }
        .summary-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .highlight {
            color: #764ba2;
            font-weight: 600;
        }
        .future-visual {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        .future-title {
            font-size: 24px;
            font-weight: 600;
            color: #764ba2;
            margin-bottom: 25px;
            text-align: center;
        }
        .trend-list {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }
        .trend-item {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 12px;
            padding: 15px;
            transition: transform 0.3s ease;
        }
        .trend-item:hover {
            transform: translateX(10px);
            background-color: #f0f7ff;
        }
        .trend-icon {
            color: #764ba2;
            margin-right: 15px;
            font-size: 24px;
            flex-shrink: 0;
        }
        .trend-text {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }
        .transition-hint {
            margin-top: 20px;
            padding: 15px;
            background: linear-gradient(90deg, rgba(118, 75, 162, 0.2) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        .transition-text {
            font-size: 18px;
            font-weight: 600;
            color: #764ba2;
        }
        .transition-arrow {
            font-size: 24px;
            color: #764ba2;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: translateX(0); }
            50% { transform: translateX(10px); }
            100% { transform: translateX(0); }
        }
        .footer {
            padding: 20px 70px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
        .glow {
            position: absolute;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(118, 75, 162, 0.3) 0%, rgba(118, 75, 162, 0) 70%);
            z-index: 0;
        }
        .glow-1 {
            top: -50px;
            right: -50px;
        }
        .glow-2 {
            bottom: -50px;
            left: -50px;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <div class="left-content">
                <h1 class="title">AI的未来：从理解到创造</h1>
                
                <div class="summary-card">
                    <div class="summary-header">
                        <div class="summary-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="summary-title">培训内容回顾</div>
                    </div>
                    <div class="summary-content">
                        • <span class="highlight">基础概念</span>：大语言模型的本质构成与工作原理<br>
                        • <span class="highlight">核心术语</span>：Prompt、分词器、Token的作用机制<br>
                        • <span class="highlight">模型家族</span>：蒸馏、量化、稠密、稀疏、MOE各类型特点<br>
                        • <span class="highlight">训练成长</span>：预训练与后训练的完整流程<br>
                        • <span class="highlight">AIGC能力</span>：多模态生成与应用场景<br>
                        • <span class="highlight">超能力扩展</span>：RAG、Function Calling、MCP、Agent技术栈
                    </div>
                </div>
                
                <div class="summary-card">
                    <div class="summary-header">
                        <div class="summary-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="summary-title">学习成果与展望</div>
                    </div>
                    <div class="summary-content">
                        • 我们已了解大语言模型的<span class="highlight">基础知识</span><br>
                        • 接下来将探索大模型的<span class="highlight">生态系统</span>和<span class="highlight">应用场景</span><br>
                        • 随着技术发展，需要思考如何<span class="highlight">实际应用</span>这些技术<br>
                        • 以及它们将如何改变我们的<span class="highlight">工作方式</span>和<span class="highlight">思维模式</span>
                    </div>
                </div>
            </div>
            
            <div class="right-content">
                <div class="future-visual">
                    <div class="glow glow-1"></div>
                    <div class="glow glow-2"></div>
                    
                    <div class="future-title">AI技术发展路线图</div>
                    
                    <div class="trend-list">
                        <div class="trend-item">
                            <i class="fas fa-brain trend-icon"></i>
                            <div class="trend-text">理解AI原理与机制</div>
                        </div>
                        
                        <div class="trend-item">
                            <i class="fas fa-network-wired trend-icon"></i>
                            <div class="trend-text">探索大模型生态系统</div>
                        </div>
                        
                        <div class="trend-item">
                            <i class="fas fa-puzzle-piece trend-icon"></i>
                            <div class="trend-text">掌握实际应用场景</div>
                        </div>
                        
                        <div class="trend-item">
                            <i class="fas fa-sync-alt trend-icon"></i>
                            <div class="trend-text">转变工作思维模式</div>
                        </div>
                        
                        <div class="trend-item">
                            <i class="fas fa-magic trend-icon"></i>
                            <div class="trend-text">精通提示词工程技巧</div>
                        </div>
                    </div>
                    
                    <div class="transition-hint">
                        <div class="transition-text">下一章：大模型的生态系统与应用场景</div>
                        <i class="fas fa-arrow-right transition-arrow"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第12页
        </div>
    </div>
</body>
</html>