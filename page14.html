<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大模型的'十八般武艺'：应用场景大盘点</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 70px;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
        }
        .scene-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 25px;
            flex-grow: 1;
        }
        .scene-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .scene-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .scene-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .scene-icon {
            background-color: #6a11cb;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .scene-title {
            font-size: 22px;
            font-weight: 600;
            color: #333;
        }
        .scene-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
            flex-grow: 1;
        }
        .scene-metaphor {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
            font-size: 18px;
            color: #6a11cb;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        .metaphor-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        .highlight {
            color: #6a11cb;
            font-weight: 600;
        }
        .scene-examples {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .example-tag {
            background-color: #f0f7ff;
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 16px;
            color: #6a11cb;
            font-weight: 500;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">大模型的'十八般武艺'：应用场景大盘点</h1>
            
            <div class="scene-grid">
                <div class="scene-card">
                    <div class="scene-header">
                        <div class="scene-icon">
                            <i class="material-icons">edit</i>
                        </div>
                        <div class="scene-title">内容创作</div>
                    </div>
                    <div class="scene-content">
                        生成各类文本内容，提供创意灵感
                    </div>
                    <div class="scene-examples">
                        <div class="example-tag">文章写作</div>
                        <div class="example-tag">诗歌创作</div>
                        <div class="example-tag">广告文案</div>
                    </div>
                    <div class="scene-metaphor">
                        <i class="material-icons metaphor-icon">auto_stories</i>
                        创意无限的作家
                    </div>
                </div>
                
                <div class="scene-card">
                    <div class="scene-header">
                        <div class="scene-icon">
                            <i class="material-icons">question_answer</i>
                        </div>
                        <div class="scene-title">知识问答</div>
                    </div>
                    <div class="scene-content">
                        解答各类问题，提供专业咨询
                    </div>
                    <div class="scene-examples">
                        <div class="example-tag">专业咨询</div>
                        <div class="example-tag">学习辅助</div>
                        <div class="example-tag">知识检索</div>
                    </div>
                    <div class="scene-metaphor">
                        <i class="material-icons metaphor-icon">psychology</i>
                        无所不知的百科全书
                    </div>
                </div>
                
                <div class="scene-card">
                    <div class="scene-header">
                        <div class="scene-icon">
                            <i class="material-icons">code</i>
                        </div>
                        <div class="scene-title">代码生成与辅助</div>
                    </div>
                    <div class="scene-content">
                        编写代码，调试程序，解释代码
                    </div>
                    <div class="scene-examples">
                        <div class="example-tag">代码编写</div>
                        <div class="example-tag">程序调试</div>
                        <div class="example-tag">代码解释</div>
                    </div>
                    <div class="scene-metaphor">
                        <i class="material-icons metaphor-icon">terminal</i>
                        编程高手
                    </div>
                </div>
                
                <div class="scene-card">
                    <div class="scene-header">
                        <div class="scene-icon">
                            <i class="material-icons">translate</i>
                        </div>
                        <div class="scene-title">语言翻译</div>
                    </div>
                    <div class="scene-content">
                        多语言互译，专业术语翻译
                    </div>
                    <div class="scene-examples">
                        <div class="example-tag">多语言互译</div>
                        <div class="example-tag">专业术语</div>
                        <div class="example-tag">本地化</div>
                    </div>
                    <div class="scene-metaphor">
                        <i class="material-icons metaphor-icon">language</i>
                        语言大师
                    </div>
                </div>
                
                <div class="scene-card">
                    <div class="scene-header">
                        <div class="scene-icon">
                            <i class="material-icons">analytics</i>
                        </div>
                        <div class="scene-title">数据分析</div>
                    </div>
                    <div class="scene-content">
                        数据解读，报告生成，趋势预测
                    </div>
                    <div class="scene-examples">
                        <div class="example-tag">数据解读</div>
                        <div class="example-tag">报告生成</div>
                        <div class="example-tag">趋势预测</div>
                    </div>
                    <div class="scene-metaphor">
                        <i class="material-icons metaphor-icon">insights</i>
                        数据分析师
                    </div>
                </div>
                
                <div class="scene-card">
                    <div class="scene-header">
                        <div class="scene-icon">
                            <i class="material-icons">support_agent</i>
                        </div>
                        <div class="scene-title">智能客服</div>
                    </div>
                    <div class="scene-content">
                        自动回复，问题解答，情感分析
                    </div>
                    <div class="scene-examples">
                        <div class="example-tag">自动回复</div>
                        <div class="example-tag">问题解答</div>
                        <div class="example-tag">情感分析</div>
                    </div>
                    <div class="scene-metaphor">
                        <i class="material-icons metaphor-icon">headset_mic</i>
                        贴心客服
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第14页
        </div>
    </div>
</body>
</html>