<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词工程技巧：提升AI对话质量的秘诀</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .techniques-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            flex-grow: 1;
        }
        .technique-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .technique-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .technique-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .technique-icon-container {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .technique-icon {
            color: white;
            font-size: 24px;
        }
        .technique-title {
            font-size: 20px;
            font-weight: 700;
            color: #333;
        }
        .technique-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .technique-desc {
            font-size: 16px;
            color: #555;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .example-box {
            background-color: #fff0f3;
            border-radius: 10px;
            padding: 12px;
            margin-top: auto;
            border-left: 4px solid #f5576c;
        }
        .example-label {
            font-size: 14px;
            font-weight: 600;
            color: #f5576c;
            margin-bottom: 5px;
        }
        .example-text {
            font-size: 14px;
            color: #666;
            font-style: italic;
        }
        .kung-fu-visual {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .kung-fu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            margin: 0 15px;
            transition: all 0.3s ease;
        }
        .kung-fu-item:hover {
            transform: scale(1.1);
        }
        .kung-fu-icon {
            font-size: 36px;
            color: #f5576c;
            margin-bottom: 8px;
        }
        .kung-fu-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">提示词工程技巧：提升AI对话质量的秘诀</h1>
            
            <div class="techniques-grid">
                <div class="technique-card">
                    <div class="technique-header">
                        <div class="technique-icon-container">
                            <i class="fas fa-user-tag technique-icon"></i>
                        </div>
                        <div class="technique-title">角色设定技巧</div>
                    </div>
                    <div class="technique-content">
                        <div class="technique-desc">
                            明确告诉AI它应该扮演什么角色，以获得更专业的回答
                        </div>
                        <div class="example-box">
                            <div class="example-label">示例</div>
                            <div class="example-text">"你是一位专业的市场营销专家，请分析..."</div>
                        </div>
                    </div>
                </div>
                
                <div class="technique-card">
                    <div class="technique-header">
                        <div class="technique-icon-container">
                            <i class="fas fa-info-circle technique-icon"></i>
                        </div>
                        <div class="technique-title">上下文提供技巧</div>
                    </div>
                    <div class="technique-content">
                        <div class="technique-desc">
                            提供足够的背景信息，帮助AI更好地理解问题
                        </div>
                        <div class="example-box">
                            <div class="example-label">示例</div>
                            <div class="example-text">"我们是一家专注于环保产品的初创公司，目标是..."</div>
                        </div>
                    </div>
                </div>
                
                <div class="technique-card">
                    <div class="technique-header">
                        <div class="technique-icon-container">
                            <i class="fas fa-file-alt technique-icon"></i>
                        </div>
                        <div class="technique-title">示例引导技巧</div>
                    </div>
                    <div class="technique-content">
                        <div class="technique-desc">
                            通过"少样本学习"提供示例，引导AI理解期望的输出格式和风格
                        </div>
                        <div class="example-box">
                            <div class="example-label">示例</div>
                            <div class="example-text">"请按照以下格式回答：问题：... 答案：..."</div>
                        </div>
                    </div>
                </div>
                
                <div class="technique-card">
                    <div class="technique-header">
                        <div class="technique-icon-container">
                            <i class="fas fa-project-diagram technique-icon"></i>
                        </div>
                        <div class="technique-title">分步思考技巧</div>
                    </div>
                    <div class="technique-content">
                        <div class="technique-desc">
                            对于复杂问题，引导AI逐步推理，提高回答质量
                        </div>
                        <div class="example-box">
                            <div class="example-label">示例</div>
                            <div class="example-text">"请先分析问题，再提出解决方案，最后评估可行性"</div>
                        </div>
                    </div>
                </div>
                
                <div class="technique-card">
                    <div class="technique-header">
                        <div class="technique-icon-container">
                            <i class="fas fa-filter technique-icon"></i>
                        </div>
                        <div class="technique-title">约束条件技巧</div>
                    </div>
                    <div class="technique-content">
                        <div class="technique-desc">
                            明确限制和约束，控制AI输出的范围和格式
                        </div>
                        <div class="example-box">
                            <div class="example-label">示例</div>
                            <div class="example-text">"回答不超过200字，请使用通俗易懂的语言"</div>
                        </div>
                    </div>
                </div>
                
                <div class="technique-card">
                    <div class="technique-header">
                        <div class="technique-icon-container">
                            <i class="fas fa-sync-alt technique-icon"></i>
                        </div>
                        <div class="technique-title">迭代优化技巧</div>
                    </div>
                    <div class="technique-content">
                        <div class="technique-desc">
                            根据AI的反馈不断调整和优化提示词，持续改进结果
                        </div>
                        <div class="example-box">
                            <div class="example-label">示例</div>
                            <div class="example-text">"你的回答不够具体，请提供更多细节和数据支持"</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="kung-fu-visual">
                <div class="kung-fu-item">
                    <i class="fas fa-fist-raised kung-fu-icon"></i>
                    <div class="kung-fu-text">角色设定</div>
                </div>
                
                <div class="kung-fu-item">
                    <i class="fas fa-brain kung-fu-icon"></i>
                    <div class="kung-fu-text">上下文提供</div>
                </div>
                
                <div class="kung-fu-item">
                    <i class="fas fa-book kung-fu-icon"></i>
                    <div class="kung-fu-text">示例引导</div>
                </div>
                
                <div class="kung-fu-item">
                    <i class="fas fa-sitemap kung-fu-icon"></i>
                    <div class="kung-fu-text">分步思考</div>
                </div>
                
                <div class="kung-fu-item">
                    <i class="fas fa-sliders-h kung-fu-icon"></i>
                    <div class="kung-fu-text">约束条件</div>
                </div>
                
                <div class="kung-fu-item">
                    <i class="fas fa-redo kung-fu-icon"></i>
                    <div class="kung-fu-text">迭代优化</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第26页
        </div>
    </div>
</body>
</html>