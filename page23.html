<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI时代的'伦理罗盘'：伦理与责任</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 60px;
        }
        .title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 30px;
            color: #ffffff;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
        .ethics-container {
            display: flex;
            gap: 25px;
            flex-grow: 1;
        }
        .ethics-left {
            width: 60%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .ethics-right {
            width: 40%;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .ethics-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .ethics-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .card-icon {
            background-color: #0066cc;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 24px;
        }
        .card-title {
            font-size: 22px;
            font-weight: 700;
            color: #0066cc;
        }
        .importance-content {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }
        .importance-icon {
            font-size: 60px;
            color: #0066cc;
            margin-right: 20px;
            flex-shrink: 0;
        }
        .importance-text {
            font-size: 18px;
            color: #333;
            line-height: 1.5;
        }
        .challenges-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 10px;
        }
        .challenge-item {
            background-color: #e6f7ff;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
            display: flex;
            align-items: flex-start;
        }
        .challenge-item:hover {
            transform: translateY(-5px);
            background-color: #bae7ff;
        }
        .challenge-icon {
            color: #0066cc;
            font-size: 20px;
            margin-right: 10px;
            margin-top: 2px;
            flex-shrink: 0;
        }
        .challenge-content {
            flex-grow: 1;
        }
        .challenge-title {
            font-size: 16px;
            font-weight: 600;
            color: #0066cc;
            margin-bottom: 5px;
        }
        .challenge-desc {
            font-size: 14px;
            color: #555;
        }
        .principles-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 10px;
        }
        .principle-item {
            display: flex;
            align-items: center;
            background-color: #e6f7ff;
            border-radius: 12px;
            padding: 12px;
            transition: all 0.3s ease;
        }
        .principle-item:hover {
            transform: translateX(10px);
            background-color: #bae7ff;
        }
        .principle-icon {
            color: #0066cc;
            font-size: 20px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        .principle-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .framework-steps {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 10px;
        }
        .framework-step {
            display: flex;
            align-items: center;
            background-color: #e6f7ff;
            border-radius: 12px;
            padding: 12px;
            transition: all 0.3s ease;
        }
        .framework-step:hover {
            transform: translateX(10px);
            background-color: #bae7ff;
        }
        .step-number {
            width: 30px;
            height: 30px;
            background-color: #0066cc;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: 700;
            margin-right: 12px;
            flex-shrink: 0;
        }
        .step-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .compass-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
        }
        .compass {
            width: 120px;
            height: 120px;
            background-color: #0066cc;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            box-shadow: 0 8px 20px rgba(0, 102, 204, 0.3);
        }
        .compass-icon {
            color: white;
            font-size: 60px;
        }
        .compass-direction {
            position: absolute;
            color: white;
            font-weight: 700;
            font-size: 14px;
        }
        .direction-n { top: 10px; }
        .direction-e { right: 10px; }
        .direction-s { bottom: 10px; }
        .direction-w { left: 10px; }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">AI时代的'伦理罗盘'：伦理与责任</h1>
            
            <div class="ethics-container">
                <div class="ethics-left">
                    <div class="ethics-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <div class="card-title">AI伦理的重要性</div>
                        </div>
                        
                        <div class="importance-content">
                            <i class="fas fa-compass importance-icon"></i>
                            <div class="importance-text">
                                确保AI技术发展符合人类价值观，避免技术滥用和潜在危害，引导AI在正确的方向上发展
                            </div>
                        </div>
                    </div>
                    
                    <div class="ethics-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-title">主要伦理挑战</div>
                        </div>
                        
                        <div class="challenges-grid">
                            <div class="challenge-item">
                                <i class="fas fa-unbalance challenge-icon"></i>
                                <div class="challenge-content">
                                    <div class="challenge-title">偏见与公平性</div>
                                    <div class="challenge-desc">AI可能继承和放大训练数据中的偏见</div>
                                </div>
                            </div>
                            
                            <div class="challenge-item">
                                <i class="fas fa-user-shield challenge-icon"></i>
                                <div class="challenge-content">
                                    <div class="challenge-title">隐私与安全</div>
                                    <div class="challenge-desc">AI系统可能收集和滥用个人数据</div>
                                </div>
                            </div>
                            
                            <div class="challenge-item">
                                <i class="fas fa-eye challenge-icon"></i>
                                <div class="challenge-content">
                                    <div class="challenge-title">透明度与可解释性</div>
                                    <div class="challenge-desc">AI决策过程难以理解和解释</div>
                                </div>
                            </div>
                            
                            <div class="challenge-item">
                                <i class="fas fa-gavel challenge-icon"></i>
                                <div class="challenge-content">
                                    <div class="challenge-title">责任归属</div>
                                    <div class="challenge-desc">AI系统造成损害的责任划分问题</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="ethics-right">
                    <div class="ethics-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-hand-holding-heart"></i>
                            </div>
                            <div class="card-title">负责任的AI使用</div>
                        </div>
                        
                        <div class="principles-list">
                            <div class="principle-item">
                                <i class="fas fa-user-lock principle-icon"></i>
                                <div class="principle-text">尊重用户隐私</div>
                            </div>
                            
                            <div class="principle-item">
                                <i class="fas fa-balance-scale-right principle-icon"></i>
                                <div class="principle-text">确保公平性</div>
                            </div>
                            
                            <div class="principle-item">
                                <i class="fas fa-search principle-icon"></i>
                                <div class="principle-text">提高透明度</div>
                            </div>
                            
                            <div class="principle-item">
                                <i class="fas fa-attribution principle-icon"></i>
                                <div class="principle-text">明确责任归属</div>
                            </div>
                            
                            <div class="principle-item">
                                <i class="fas fa-globe-americas principle-icon"></i>
                                <div class="principle-text">关注社会影响</div>
                            </div>
                        </div>
                        
                        <div class="compass-visual">
                            <div class="compass">
                                <i class="fas fa-compass compass-icon"></i>
                                <div class="compass-direction direction-n">公正</div>
                                <div class="compass-direction direction-e">透明</div>
                                <div class="compass-direction direction-s">责任</div>
                                <div class="compass-direction direction-w">隐私</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ethics-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <div class="card-title">企业AI伦理框架</div>
                        </div>
                        
                        <div class="framework-steps">
                            <div class="framework-step">
                                <div class="step-number">1</div>
                                <div class="step-text">建立AI伦理委员会</div>
                            </div>
                            
                            <div class="framework-step">
                                <div class="step-number">2</div>
                                <div class="step-text">制定AI使用准则</div>
                            </div>
                            
                            <div class="framework-step">
                                <div class="step-number">3</div>
                                <div class="step-text">定期进行伦理审查</div>
                            </div>
                            
                            <div class="framework-step">
                                <div class="step-number">4</div>
                                <div class="step-text">培养员工AI伦理意识</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第23页
        </div>
    </div>
</body>
</html>