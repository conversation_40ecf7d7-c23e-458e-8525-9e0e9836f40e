<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI培训幻灯片演示</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #000;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        html {
            overflow: hidden;
        }

        .slideshow-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: #000;
            overflow: hidden;
        }

        .slide-frame {
            width: 100vw;
            height: 100vh;
            border: none;
            box-shadow: none;
            border-radius: 0;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transform: translateX(100%) translateZ(0);
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                        opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 1;
            will-change: transform, opacity;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
        }

        .slide-frame.active {
            opacity: 1;
            transform: translateX(0) translateZ(0);
            z-index: 2;
        }

        .slide-frame.prev {
            transform: translateX(-100%) translateZ(0);
            opacity: 0;
        }

        .controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.1);
            padding: 10px 20px;
            border-radius: 30px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            opacity: 0.2;
            pointer-events: auto;
        }

        .controls:hover {
            background: rgba(0, 0, 0, 0.8);
            opacity: 1;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .slide-counter {
            color: white;
            font-size: 14px;
            display: flex;
            align-items: center;
            padding: 10px 15px;
        }



        .fullscreen-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 12px;
            background: rgba(0, 0, 0, 0.5);
            padding: 5px 10px;
            border-radius: 15px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .fullscreen-indicator.show {
            opacity: 1;
        }



        /* 加载动画 */
        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 2000;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="slideshow-container" id="slideshowContainer">
        <div class="loading" id="loading">正在加载幻灯片...</div>
        
        <!-- 幻灯片将通过JavaScript动态加载 -->
        


        <!-- 控制面板 -->
        <div class="controls">
            <button class="control-btn" onclick="firstSlide()">
                <span class="material-icons">first_page</span>
                首页
            </button>
            <button class="control-btn" onclick="previousSlide()">
                <span class="material-icons">navigate_before</span>
                上一页
            </button>
            <div class="slide-counter">
                <span id="currentSlide">1</span> / <span id="totalSlides">31</span>
            </div>
            <button class="control-btn" onclick="nextSlide()">
                下一页
                <span class="material-icons">navigate_next</span>
            </button>
            <button class="control-btn" onclick="lastSlide()">
                <span class="material-icons">last_page</span>
                末页
            </button>
            <button class="control-btn" onclick="toggleFullscreen()">
                <span class="material-icons" id="fullscreenIcon">fullscreen</span>
                全屏
            </button>
        </div>

        <!-- 全屏提示 -->
        <div class="fullscreen-indicator" id="fullscreenIndicator">
            按 ESC 键退出全屏 | 使用方向键切换幻灯片
        </div>
    </div>

    <script>
        let currentSlideIndex = 0;
        const totalSlides = 31;
        let slides = [];
        let isAnimating = false;

        // 初始化幻灯片
        function initSlides() {
            const container = document.getElementById('slideshowContainer');

            for (let i = 1; i <= totalSlides; i++) {
                const iframe = document.createElement('iframe');
                iframe.src = `page${i}.html`;
                iframe.className = 'slide-frame';
                iframe.id = `slide-${i}`;
                iframe.frameBorder = '0';
                iframe.allowFullscreen = true;
                iframe.scrolling = 'auto';
                if (i === 1) {
                    iframe.classList.add('active');
                }
                container.appendChild(iframe);
                slides.push(iframe);
            }

            // 隐藏加载提示
            document.getElementById('loading').style.display = 'none';

            updateSlideCounter();
            updateNavigationButtons();
        }

        // 显示指定幻灯片
        function showSlide(index, direction = 'next') {
            if (index < 0 || index >= totalSlides || isAnimating) return;

            const oldIndex = currentSlideIndex;
            const newSlide = slides[index];
            const oldSlide = slides[oldIndex];

            // 如果是同一张幻灯片，直接返回
            if (index === oldIndex) return;

            // 设置动画状态
            isAnimating = true;

            // 设置新幻灯片的初始位置
            if (direction === 'next') {
                newSlide.style.transform = 'translateX(100%) translateZ(0)';
            } else {
                newSlide.style.transform = 'translateX(-100%) translateZ(0)';
            }
            newSlide.style.opacity = '0';
            newSlide.classList.add('active');

            // 强制重绘
            newSlide.offsetHeight;

            // 开始动画
            requestAnimationFrame(() => {
                // 新幻灯片滑入
                newSlide.style.transform = 'translateX(0) translateZ(0)';
                newSlide.style.opacity = '1';

                // 旧幻灯片滑出
                if (oldSlide) {
                    if (direction === 'next') {
                        oldSlide.style.transform = 'translateX(-100%) translateZ(0)';
                    } else {
                        oldSlide.style.transform = 'translateX(100%) translateZ(0)';
                    }
                    oldSlide.style.opacity = '0';
                }
            });

            // 动画完成后清理
            setTimeout(() => {
                slides.forEach((slide, i) => {
                    if (i !== index) {
                        slide.classList.remove('active');
                        slide.style.transform = '';
                        slide.style.opacity = '';
                    }
                });
                isAnimating = false; // 重置动画状态
            }, 400);

            currentSlideIndex = index;
            updateSlideCounter();
            updateNavigationButtons();
        }

        // 下一张幻灯片
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                showSlide(currentSlideIndex + 1, 'next');
            }
        }

        // 上一张幻灯片
        function previousSlide() {
            if (currentSlideIndex > 0) {
                showSlide(currentSlideIndex - 1, 'prev');
            }
        }

        // 第一张幻灯片
        function firstSlide() {
            const direction = currentSlideIndex > 0 ? 'prev' : 'next';
            showSlide(0, direction);
        }

        // 最后一张幻灯片
        function lastSlide() {
            const direction = currentSlideIndex < totalSlides - 1 ? 'next' : 'prev';
            showSlide(totalSlides - 1, direction);
        }

        // 更新幻灯片计数器
        function updateSlideCounter() {
            document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
        }

        // 更新导航按钮状态
        function updateNavigationButtons() {
            // 导航箭头已移除，此函数保留用于未来扩展
        }

        // 切换全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().then(() => {
                    document.getElementById('fullscreenIcon').textContent = 'fullscreen_exit';
                    document.getElementById('fullscreenIndicator').classList.add('show');
                    setTimeout(() => {
                        document.getElementById('fullscreenIndicator').classList.remove('show');
                    }, 3000);
                });
            } else {
                document.exitFullscreen().then(() => {
                    document.getElementById('fullscreenIcon').textContent = 'fullscreen';
                });
            }
        }



        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    event.preventDefault();
                    previousSlide();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    event.preventDefault();
                    nextSlide();
                    break;
                case 'Home':
                    event.preventDefault();
                    firstSlide();
                    break;
                case 'End':
                    event.preventDefault();
                    lastSlide();
                    break;
                case 'F11':
                    event.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    // 可以用于退出全屏等操作
                    break;
            }
        });

        // 全屏状态变化监听
        document.addEventListener('fullscreenchange', function() {
            if (!document.fullscreenElement) {
                document.getElementById('fullscreenIcon').textContent = 'fullscreen';
            }
        });

        // 页面加载完成后初始化
        window.addEventListener('load', initSlides);
    </script>
</body>
</html>
