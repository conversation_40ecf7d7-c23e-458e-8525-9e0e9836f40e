<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词工程实践：工作场景测试</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&amp;display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 30px 60px;
        }
        .title-container {
            margin-bottom: 25px;
            text-align: center;
        }
        .main-title {
            font-size: 42px;
            font-weight: 800;
            color: #ffffff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            opacity: 0.9;
        }
        .exercises-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            flex-grow: 1;
        }
        .exercise-card {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 18px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }
        .exercise-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        .exercise-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .exercise-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding-left: 6px;
        }
        .exercise-icon-container {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .exercise-icon {
            color: white;
            font-size: 22px;
        }
        .exercise-title {
            font-size: 20px;
            font-weight: 700;
            color: #e91e63;
        }
        .exercise-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding-left: 6px;
        }
        .exercise-task {
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
            font-weight: 500;
        }
        .key-points {
            margin-top: auto;
        }
        .key-point {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            background-color: rgba(250, 112, 154, 0.08);
            border-radius: 8px;
            padding: 6px 10px;
            transition: all 0.2s ease;
        }
        .key-point:hover {
            background-color: rgba(250, 112, 154, 0.15);
            transform: translateX(5px);
        }
        .key-point-icon {
            color: #e91e63;
            font-size: 16px;
            margin-right: 8px;
            margin-top: 3px;
            flex-shrink: 0;
        }
        .key-point-text {
            font-size: 15px;
            color: #555;
            line-height: 1.3;
        }
        .martial-arts-visual {
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 18px;
            margin-top: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: space-around;
        }
        .martial-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }
        .martial-item:hover {
            transform: scale(1.1);
        }
        .martial-icon {
            font-size: 32px;
            color: #e91e63;
            margin-bottom: 8px;
        }
        .martial-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        .footer {
            padding: 15px 60px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <div class="title-container">
                <h1 class="main-title">提示词工程实践：工作场景测试</h1>
                <div class="subtitle">掌握提示词工程，提升职场效率</div>
            </div>
            
            <div class="exercises-grid">
                <div class="exercise-card">
                    <div class="exercise-header">
                        <div class="exercise-icon-container">
                            <i class="fas fa-file-alt exercise-icon"></i>
                        </div>
                        <div class="exercise-title">练习一：述职报告生成</div>
                    </div>
                    <div class="exercise-content">
                        <div class="exercise-task">
                            设计提示词，让AI根据工作内容和成果数据，生成结构清晰、重点突出的述职报告
                        </div>
                        <div class="key-points">
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">设定述职人员角色</div>
                            </div>
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">提供工作背景和成果数据</div>
                            </div>
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">指定报告结构和风格</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="exercise-card">
                    <div class="exercise-header">
                        <div class="exercise-icon-container">
                            <i class="fas fa-chart-line exercise-icon"></i>
                        </div>
                        <div class="exercise-title">练习二：数据分析</div>
                    </div>
                    <div class="exercise-content">
                        <div class="exercise-task">
                            设计提示词，让AI自动分析销售数据并生成分析报告
                        </div>
                        <div class="key-points">
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">设定数据分析师角色</div>
                            </div>
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">提供数据格式和分析目标</div>
                            </div>
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">指定输出格式和关键指标</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="exercise-card">
                    <div class="exercise-header">
                        <div class="exercise-icon-container">
                            <i class="fas fa-users exercise-icon"></i>
                        </div>
                        <div class="exercise-title">练习三：会议纪要生成</div>
                    </div>
                    <div class="exercise-content">
                        <div class="exercise-task">
                            设计提示词，让AI根据会议录音或笔记生成结构化的会议纪要
                        </div>
                        <div class="key-points">
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">设定会议记录员角色</div>
                            </div>
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">提供会议背景和关键信息</div>
                            </div>
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">指定纪要格式和重点内容</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="exercise-card">
                    <div class="exercise-header">
                        <div class="exercise-icon-container">
                            <i class="fas fa-file-contract exercise-icon"></i>
                        </div>
                        <div class="exercise-title">练习四：文档总结</div>
                    </div>
                    <div class="exercise-content">
                        <div class="exercise-task">
                            设计提示词，让AI总结长篇文档并提取关键信息
                        </div>
                        <div class="key-points">
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">设定人员角色</div>
                            </div>
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">提供文档背景和总结目标</div>
                            </div>
                            <div class="key-point">
                                <i class="fas fa-check-circle key-point-icon"></i>
                                <div class="key-point-text">指定输出格式和重点内容</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="martial-arts-visual">
                <div class="martial-item">
                    <i class="fas fa-briefcase martial-icon"></i>
                    <div class="martial-text">实际工作场景</div>
                </div>
                
                <i class="fas fa-plus text-pink-500 text-2xl"></i>
                
                <div class="martial-item">
                    <i class="fas fa-magic martial-icon"></i>
                    <div class="martial-text">提示词工程</div>
                </div>
                
                <i class="fas fa-equals text-pink-500 text-2xl"></i>
                
                <div class="martial-item">
                    <i class="fas fa-trophy martial-icon"></i>
                    <div class="martial-text">办公效率提升</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第29页
        </div>
    </div>

</body></html>