<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIGC与模态：能力与局限</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            padding: 40px 0;
        }
        .left-content {
            width: 50%;
            padding-left: 70px;
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .right-content {
            width: 50%;
            padding-left: 20px;
            padding-right: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 40px;
            color: #8B0000;
        }
        .concept-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .concept-card:hover {
            transform: translateY(-5px);
        }
        .concept-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .concept-icon {
            background-color: #FF6B6B;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .concept-title {
            font-size: 22px;
            font-weight: 600;
            color: #8B0000;
        }
        .concept-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .highlight {
            color: #FF6B6B;
            font-weight: 600;
        }
        .visual-container {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 500px;
        }
        .modality-section {
            margin-bottom: 30px;
        }
        .modality-title {
            font-size: 24px;
            font-weight: 600;
            color: #8B0000;
            margin-bottom: 20px;
            text-align: center;
        }
        .modality-types {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .modality-type {
            width: 45%;
            background-color: #fff5f5;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 2px solid #FFE4E1;
        }
        .type-icon {
            font-size: 40px;
            color: #FF6B6B;
            margin-bottom: 10px;
        }
        .type-title {
            font-size: 18px;
            font-weight: 600;
            color: #8B0000;
            margin-bottom: 8px;
        }
        .type-desc {
            font-size: 16px;
            color: #555;
            line-height: 1.4;
        }
        .limitations-section {
            background-color: #FFF0F0;
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #FF6B6B;
        }
        .limitations-title {
            font-size: 20px;
            font-weight: 600;
            color: #8B0000;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .limitations-title i {
            margin-right: 10px;
            color: #FF6B6B;
        }
        .limitation-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px;
            background-color: white;
            border-radius: 8px;
        }
        .limitation-icon {
            font-size: 20px;
            color: #FF6B6B;
            margin-right: 12px;
        }
        .limitation-text {
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(139, 0, 0, 0.7);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <div class="left-content">
                <h1 class="title">AIGC与模态：能力与局限</h1>
                
                <div class="concept-card">
                    <div class="concept-header">
                        <div class="concept-icon">
                            <i class="material-icons">auto_awesome</i>
                        </div>
                        <div class="concept-title">AIGC</div>
                    </div>
                    <div class="concept-content">
                        <span class="highlight">AI Generated Content</span>（人工智能生成内容），利用AI技术自动生成<span class="highlight">文本、图像、音频、视频</span>等多种形式的内容
                    </div>
                </div>
                
                <div class="concept-card">
                    <div class="concept-header">
                        <div class="concept-icon">
                            <i class="material-icons">text_fields</i>
                        </div>
                        <div class="concept-title">单模态</div>
                    </div>
                    <div class="concept-content">
                        只能处理<span class="highlight">一种类型</span>的数据输入，如纯文本模型只能理解和生成文字内容
                    </div>
                </div>
                
                <div class="concept-card">
                    <div class="concept-header">
                        <div class="concept-icon">
                            <i class="material-icons">view_module</i>
                        </div>
                        <div class="concept-title">多模态</div>
                    </div>
                    <div class="concept-content">
                        能够同时处理<span class="highlight">多种类型</span>的数据输入，如文本+图像+音频，实现跨模态的理解和生成
                    </div>
                </div>
            </div>
            
            <div class="right-content">
                <div class="visual-container">
                    <div class="modality-section">
                        <div class="modality-title">模态对比</div>
                        <div class="modality-types">
                            <div class="modality-type">
                                <i class="material-icons type-icon">text_fields</i>
                                <div class="type-title">单模态</div>
                                <div class="type-desc">专注单一数据类型<br>处理效率高<br>应用场景相对局限</div>
                            </div>
                            
                            <div class="modality-type">
                                <i class="material-icons type-icon">dashboard</i>
                                <div class="type-title">多模态</div>
                                <div class="type-desc">融合多种数据类型<br>理解更全面<br>应用场景更广泛</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="limitations-section">
                        <div class="limitations-title">
                            <i class="material-icons">warning</i>
                            AIGC的两大短板
                        </div>
                        
                        <div class="limitation-item">
                            <i class="material-icons limitation-icon">schedule</i>
                            <div class="limitation-text">不具备实时性</div>
                        </div>
                        
                        <div class="limitation-item">
                            <i class="material-icons limitation-icon">build</i>
                            <div class="limitation-text">不会使用工具</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第9页
        </div>
    </div>
</body>
</html>
