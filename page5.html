<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大语言模型的'家族'：各种模型类型</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 70px;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 30px;
            color: #5D4037;
            text-align: center;
        }
        .model-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            flex-grow: 1;
        }
        .model-card {
            width: calc(33.33% - 14px);
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        }
        .model-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .model-icon {
            background-color: #8E24AA;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .model-title {
            font-size: 22px;
            font-weight: 600;
            color: #5D4037;
        }
        .model-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
            flex-grow: 1;
        }
        .model-metaphor {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
            font-size: 18px;
            color: #7B5E57;
            font-style: italic;
        }
        .highlight {
            color: #8E24AA;
            font-weight: 600;
        }
        .moe-visual {
            margin-top: 30px;
            background-color: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .moe-left {
            width: 60%;
        }
        .moe-title {
            font-size: 24px;
            font-weight: 600;
            color: #5D4037;
            margin-bottom: 15px;
        }
        .moe-desc {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .moe-right {
            width: 35%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .superhero-team {
            display: flex;
            justify-content: center;
            align-items: flex-end;
            height: 180px;
        }
        .superhero {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 10px;
        }
        .hero-icon {
            font-size: 40px;
            color: #8E24AA;
            margin-bottom: 10px;
        }
        .hero-name {
            font-size: 16px;
            font-weight: 600;
            color: #5D4037;
            text-align: center;
        }
        .gate-icon {
            font-size: 50px;
            color: #FF9800;
            margin-top: -20px;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(93, 64, 55, 0.7);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">大语言模型的'家族'：各种模型类型</h1>
            
            <div class="model-grid">
                <div class="model-card">
                    <div class="model-header">
                        <div class="model-icon">
                            <i class="material-icons">local_drink</i>
                        </div>
                        <div class="model-title">蒸馏模型</div>
                    </div>
                    <div class="model-content">
                        将大模型的<span class="highlight">知识"蒸馏"</span>到小模型中
                    </div>
                    <div class="model-metaphor">
                        像将浓缩果汁稀释成适合饮用的果汁
                    </div>
                </div>
                
                <div class="model-card">
                    <div class="model-header">
                        <div class="model-icon">
                            <i class="material-icons">photo_size_select_small</i>
                        </div>
                        <div class="model-title">量化模型</div>
                    </div>
                    <div class="model-content">
                        减少模型参数的<span class="highlight">精度</span>，节省空间
                    </div>
                    <div class="model-metaphor">
                        像将高清图片压缩成低分辨率图片
                    </div>
                </div>
                
                <div class="model-card">
                    <div class="model-header">
                        <div class="model-icon">
                            <i class="material-icons">groups</i>
                        </div>
                        <div class="model-title">稠密模型</div>
                    </div>
                    <div class="model-content">
                        <span class="highlight">所有参数</span>都参与计算
                    </div>
                    <div class="model-metaphor">
                        像团队中每个人都参与工作
                    </div>
                </div>
                
                <div class="model-card">
                    <div class="model-header">
                        <div class="model-icon">
                            <i class="material-icons">person_search</i>
                        </div>
                        <div class="model-title">稀疏模型</div>
                    </div>
                    <div class="model-content">
                        只有<span class="highlight">部分参数</span>参与计算
                    </div>
                    <div class="model-metaphor">
                        像团队中只有专家参与特定任务
                    </div>
                </div>
                
                <div class="model-card">
                    <div class="model-header">
                        <div class="model-icon">
                            <i class="material-icons">diversity_3</i>
                        </div>
                        <div class="model-title">MOE混合专家</div>
                    </div>
                    <div class="model-content">
                        由多个<span class="highlight">专家</span>组成的团队，各司其职
                    </div>
                    <div class="model-metaphor">
                        像超级英雄联盟，每个英雄都有自己的特长
                    </div>
                </div>
            </div>
            
            <div class="moe-visual">
                <div class="moe-left">
                    <div class="moe-title">MOE（混合专家模型）工作原理</div>
                    <div class="moe-desc">
                        多个专业化的子模型（"专家"）组合而成，每个"专家"都在其擅长的领域内做出贡献。门控网络决定哪个"专家"参与解答特定问题，实现高效计算。
                    </div>
                </div>
                <div class="moe-right">
                    <div class="superhero-team">
                        <div class="superhero">
                            <i class="material-icons hero-icon">science</i>
                            <div class="hero-name">科学专家</div>
                        </div>
                        <div class="superhero">
                            <i class="material-icons hero-icon">calculate</i>
                            <div class="hero-name">数学专家</div>
                        </div>
                        <div class="superhero">
                            <i class="material-icons gate-icon">hub</i>
                            <div class="hero-name">门控网络</div>
                        </div>
                        <div class="superhero">
                            <i class="material-icons hero-icon">language</i>
                            <div class="hero-name">语言专家</div>
                        </div>
                        <div class="superhero">
                            <i class="material-icons hero-icon">code</i>
                            <div class="hero-name">编程专家</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第5页
        </div>
    </div>
</body>
</html>