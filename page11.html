<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>两种路径：MCP vs Function Calling</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 40px 70px;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 30px;
            color: #d81b60;
            text-align: center;
        }
        .comparison-container {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
            flex-grow: 1;
        }
        .comparison-column {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .comparison-header {
            display: flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px 16px 0 0;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .approach-icon {
            background-color: #d81b60;
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20px;
            flex-shrink: 0;
            font-size: 30px;
        }
        .approach-title {
            font-size: 24px;
            font-weight: 600;
            color: #d81b60;
        }
        .approach-subtitle {
            font-size: 18px;
            color: #666;
            margin-top: 5px;
        }
        .comparison-body {
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 0 0 16px 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .aspect-card {
            background-color: #fff;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }
        .aspect-card:hover {
            transform: translateY(-3px);
        }
        .aspect-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .aspect-icon {
            color: #d81b60;
            margin-right: 10px;
            font-size: 24px;
        }
        .aspect-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        .aspect-content {
            font-size: 18px;
            color: #555;
            line-height: 1.4;
        }
        .highlight {
            color: #d81b60;
            font-weight: 600;
        }
        .metaphor-container {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        .metaphor-card {
            width: 48%;
            background-color: #fff;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .metaphor-icon {
            font-size: 40px;
            color: #d81b60;
            margin-bottom: 10px;
        }
        .metaphor-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .metaphor-desc {
            font-size: 16px;
            color: #666;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(216, 27, 96, 0.7);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <h1 class="title">两种路径：MCP vs Function Calling</h1>
            
            <div class="comparison-container">
                <div class="comparison-column">
                    <div class="comparison-header">
                        <div class="approach-icon">
                            <i class="material-icons">support_agent</i>
                        </div>
                        <div>
                            <div class="approach-title">Function Calling</div>
                            <div class="approach-subtitle">单体式模式</div>
                        </div>
                    </div>
                    
                    <div class="comparison-body">
                        <div>
                            <div class="aspect-card">
                                <div class="aspect-header">
                                    <i class="material-icons aspect-icon">architecture</i>
                                    <div class="aspect-title">结构</div>
                                </div>
                                <div class="aspect-content">
                                    模型<span class="highlight">直接调用</span>外部函数，强调轻量、快速
                                </div>
                            </div>
                            
                            <div class="aspect-card">
                                <div class="aspect-header">
                                    <i class="material-icons aspect-icon">extension</i>
                                    <div class="aspect-title">扩展性</div>
                                </div>
                                <div class="aspect-content">
                                    <span class="highlight">紧耦合</span>设计，维护成本高，跨模型迁移需额外适配
                                </div>
                            </div>
                            
                            <div class="aspect-card">
                                <div class="aspect-header">
                                    <i class="material-icons aspect-icon">speed</i>
                                    <div class="aspect-title">性能</div>
                                </div>
                                <div class="aspect-content">
                                    单次任务响应<span class="highlight">延迟低</span>，适合低并发、轻量任务
                                </div>
                            </div>
                        </div>
                        
                        <div class="metaphor-container">
                            <div class="metaphor-card">
                                <i class="material-icons metaphor-icon">restaurant</i>
                                <div class="metaphor-title">个人厨师</div>
                                <div class="metaphor-desc">快速响应，灵活适应</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="comparison-column">
                    <div class="comparison-header">
                        <div class="approach-icon">
                            <i class="material-icons">settings_input_hdmi</i>
                        </div>
                        <div>
                            <div class="approach-title">MCP</div>
                            <div class="approach-subtitle">Client-Server架构</div>
                        </div>
                    </div>
                    
                    <div class="comparison-body">
                        <div>
                            <div class="aspect-card">
                                <div class="aspect-header">
                                    <i class="material-icons aspect-icon">architecture</i>
                                    <div class="aspect-title">结构</div>
                                </div>
                                <div class="aspect-content">
                                    工具管理与模型推理<span class="highlight">解耦</span>，标准化和模块化
                                </div>
                            </div>
                            
                            <div class="aspect-card">
                                <div class="aspect-header">
                                    <i class="material-icons aspect-icon">extension</i>
                                    <div class="aspect-title">扩展性</div>
                                </div>
                                <div class="aspect-content">
                                    <span class="highlight">松耦合</span>设计，Server端可独立更新工具，支持多源融合
                                </div>
                            </div>
                            
                            <div class="aspect-card">
                                <div class="aspect-header">
                                    <i class="material-icons aspect-icon">speed</i>
                                    <div class="aspect-title">性能</div>
                                </div>
                                <div class="aspect-content">
                                    复杂任务密集型场景下<span class="highlight">更稳定</span>，支持分布式部署
                                </div>
                            </div>
                        </div>
                        
                        <div class="metaphor-container">
                            <div class="metaphor-card">
                                <i class="material-icons metaphor-icon">storefront</i>
                                <div class="metaphor-title">连锁餐厅</div>
                                <div class="metaphor-desc">标准化流程，易于扩展</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="aspect-card" style="margin-top: 10px;">
                <div class="aspect-header">
                    <i class="material-icons aspect-icon">category</i>
                    <div class="aspect-title">适用场景</div>
                </div>
                <div class="aspect-content" style="display: flex; justify-content: space-between;">
                    <div style="width: 48%;">
                        <span class="highlight">Function Calling:</span> 面向API的应用开发、快速原型验证、智能推理驱动的复杂任务
                    </div>
                    <div style="width: 48%;">
                        <span class="highlight">MCP:</span> 大型、标准化、安全敏感的企业级系统建设
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第11页
        </div>
    </div>
</body>
</html>