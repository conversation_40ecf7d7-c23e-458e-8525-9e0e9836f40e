<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大语言模型的'成长'：预训练与后训练</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            display: flex;
            flex-direction: column;
            margin: auto;
        }
        .content {
            flex-grow: 1;
            display: flex;
            padding: 40px 0;
        }
        .left-content {
            width: 50%;
            padding-left: 70px;
            padding-right: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .right-content {
            width: 50%;
            padding-left: 20px;
            padding-right: 70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 40px;
            color: #006064;
        }
        .training-card {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .training-card:hover {
            transform: translateY(-5px);
        }
        .training-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .training-icon {
            background-color: #00838f;
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .training-title {
            font-size: 22px;
            font-weight: 600;
            color: #006064;
        }
        .training-content {
            font-size: 20px;
            color: #333;
            line-height: 1.5;
        }
        .highlight {
            color: #00838f;
            font-weight: 600;
        }
        .education-visual {
            background-color: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 500px;
        }
        .education-path {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .education-stage {
            width: 45%;
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            position: relative;
        }
        .pre-training {
            background-color: #e0f7fa;
        }
        .post-training {
            background-color: #e8f5e9;
        }
        .stage-icon {
            font-size: 50px;
            color: #00838f;
            margin-bottom: 15px;
        }
        .stage-title {
            font-size: 22px;
            font-weight: 600;
            color: #006064;
            margin-bottom: 10px;
        }
        .stage-desc {
            font-size: 18px;
            color: #333;
        }
        .value-cards {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        .value-card {
            width: 31%;
            background-color: #f5f5f5;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
        .value-icon {
            font-size: 30px;
            color: #00838f;
            margin-bottom: 10px;
        }
        .value-title {
            font-size: 18px;
            font-weight: 600;
            color: #006064;
            margin-bottom: 5px;
        }
        .value-desc {
            font-size: 16px;
            color: #555;
        }
        .footer {
            padding: 20px 70px;
            color: rgba(0, 96, 100, 0.7);
            font-size: 16px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="content">
            <div class="left-content">
                <h1 class="title">大语言模型的'成长'：预训练与后训练</h1>
                
                <div class="training-card">
                    <div class="training-header">
                        <div class="training-icon">
                            <i class="material-icons">school</i>
                        </div>
                        <div class="training-title">预训练</div>
                    </div>
                    <div class="training-content">
                        通过<span class="highlight">海量文本数据</span>的自我监督学习，使模型掌握基本的语言规律和世界知识，就像人类的<span class="highlight">"基础教育"</span>
                    </div>
                </div>
                
                <div class="training-card">
                    <div class="training-header">
                        <div class="training-icon">
                            <i class="material-icons">workspace_premium</i>
                        </div>
                        <div class="training-title">后训练</div>
                    </div>
                    <div class="training-content">
                        在预训练模型基础上，针对<span class="highlight">特定任务</span>进行额外训练，给模型能力进行<span class="highlight">"塑型"</span>，就像人类的<span class="highlight">"专业培训"</span>
                    </div>
                </div>
                
                <div class="training-card">
                    <div class="training-header">
                        <div class="training-icon">
                            <i class="material-icons">stars</i>
                        </div>
                        <div class="training-title">后训练核心价值</div>
                    </div>
                    <div class="training-content">
                        • <span class="highlight">知识精炼</span>：修正预训练阶段的知识偏差<br>
                        • <span class="highlight">能力对齐</span>：使模型输出符合人类价值观<br>
                        • <span class="highlight">推理增强</span>：赋予模型高级认知能力
                    </div>
                </div>
            </div>
            
            <div class="right-content">
                <div class="education-visual">
                    <div class="education-path">
                        <div class="education-stage pre-training">
                            <i class="material-icons stage-icon">child_care</i>
                            <div class="stage-title">预训练</div>
                            <div class="stage-desc">上幼儿园和小学<br>学习基础知识</div>
                        </div>
                        
                        <div class="education-stage post-training">
                            <i class="material-icons stage-icon">psychology</i>
                            <div class="stage-title">后训练</div>
                            <div class="stage-desc">上大学和专业培训<br>掌握专业技能</div>
                        </div>
                    </div>
                    
                    <div class="value-cards">
                        <div class="value-card">
                            <i class="material-icons value-icon">auto_fix_high</i>
                            <div class="value-title">知识精炼</div>
                            <div class="value-desc">修正知识偏差</div>
                        </div>
                        
                        <div class="value-card">
                            <i class="material-icons value-icon">handshake</i>
                            <div class="value-title">能力对齐</div>
                            <div class="value-desc">符合人类价值观</div>
                        </div>
                        
                        <div class="value-card">
                            <i class="material-icons value-icon">lightbulb</i>
                            <div class="value-title">推理增强</div>
                            <div class="value-desc">高级认知能力</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            全员AI培训系列 | 第7页
        </div>
    </div>
</body>
</html>